// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symptom_management_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SymptomManagementFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SymptomManagementFailureCopyWith<$Res> {
  factory $SymptomManagementFailureCopyWith(SymptomManagementFailure value,
          $Res Function(SymptomManagementFailure) then) =
      _$SymptomManagementFailureCopyWithImpl<$Res, SymptomManagementFailure>;
}

/// @nodoc
class _$SymptomManagementFailureCopyWithImpl<$Res,
        $Val extends SymptomManagementFailure>
    implements $SymptomManagementFailureCopyWith<$Res> {
  _$SymptomManagementFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$NetworkFailureImplCopyWith<$Res> {
  factory _$$NetworkFailureImplCopyWith(_$NetworkFailureImpl value,
          $Res Function(_$NetworkFailureImpl) then) =
      __$$NetworkFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NetworkFailureImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$NetworkFailureImpl>
    implements _$$NetworkFailureImplCopyWith<$Res> {
  __$$NetworkFailureImplCopyWithImpl(
      _$NetworkFailureImpl _value, $Res Function(_$NetworkFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NetworkFailureImpl implements NetworkFailure {
  const _$NetworkFailureImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.networkFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NetworkFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return networkFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return networkFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (networkFailure != null) {
      return networkFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return networkFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return networkFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (networkFailure != null) {
      return networkFailure(this);
    }
    return orElse();
  }
}

abstract class NetworkFailure implements SymptomManagementFailure {
  const factory NetworkFailure() = _$NetworkFailureImpl;
}

/// @nodoc
abstract class _$$CacheFailureImplCopyWith<$Res> {
  factory _$$CacheFailureImplCopyWith(
          _$CacheFailureImpl value, $Res Function(_$CacheFailureImpl) then) =
      __$$CacheFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CacheFailureImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$CacheFailureImpl>
    implements _$$CacheFailureImplCopyWith<$Res> {
  __$$CacheFailureImplCopyWithImpl(
      _$CacheFailureImpl _value, $Res Function(_$CacheFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CacheFailureImpl implements CacheFailure {
  const _$CacheFailureImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.cacheFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CacheFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return cacheFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return cacheFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (cacheFailure != null) {
      return cacheFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return cacheFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return cacheFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (cacheFailure != null) {
      return cacheFailure(this);
    }
    return orElse();
  }
}

abstract class CacheFailure implements SymptomManagementFailure {
  const factory CacheFailure() = _$CacheFailureImpl;
}

/// @nodoc
abstract class _$$SyncFailureImplCopyWith<$Res> {
  factory _$$SyncFailureImplCopyWith(
          _$SyncFailureImpl value, $Res Function(_$SyncFailureImpl) then) =
      __$$SyncFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SyncFailureImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$SyncFailureImpl>
    implements _$$SyncFailureImplCopyWith<$Res> {
  __$$SyncFailureImplCopyWithImpl(
      _$SyncFailureImpl _value, $Res Function(_$SyncFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SyncFailureImpl implements SyncFailure {
  const _$SyncFailureImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.syncFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SyncFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return syncFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return syncFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (syncFailure != null) {
      return syncFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return syncFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return syncFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (syncFailure != null) {
      return syncFailure(this);
    }
    return orElse();
  }
}

abstract class SyncFailure implements SymptomManagementFailure {
  const factory SyncFailure() = _$SyncFailureImpl;
}

/// @nodoc
abstract class _$$DownloadFailureImplCopyWith<$Res> {
  factory _$$DownloadFailureImplCopyWith(_$DownloadFailureImpl value,
          $Res Function(_$DownloadFailureImpl) then) =
      __$$DownloadFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DownloadFailureImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$DownloadFailureImpl>
    implements _$$DownloadFailureImplCopyWith<$Res> {
  __$$DownloadFailureImplCopyWithImpl(
      _$DownloadFailureImpl _value, $Res Function(_$DownloadFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DownloadFailureImpl implements DownloadFailure {
  const _$DownloadFailureImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.downloadFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DownloadFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return downloadFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return downloadFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (downloadFailure != null) {
      return downloadFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return downloadFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return downloadFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (downloadFailure != null) {
      return downloadFailure(this);
    }
    return orElse();
  }
}

abstract class DownloadFailure implements SymptomManagementFailure {
  const factory DownloadFailure() = _$DownloadFailureImpl;
}

/// @nodoc
abstract class _$$StorageFailureImplCopyWith<$Res> {
  factory _$$StorageFailureImplCopyWith(_$StorageFailureImpl value,
          $Res Function(_$StorageFailureImpl) then) =
      __$$StorageFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StorageFailureImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$StorageFailureImpl>
    implements _$$StorageFailureImplCopyWith<$Res> {
  __$$StorageFailureImplCopyWithImpl(
      _$StorageFailureImpl _value, $Res Function(_$StorageFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StorageFailureImpl implements StorageFailure {
  const _$StorageFailureImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.storageFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StorageFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return storageFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return storageFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (storageFailure != null) {
      return storageFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return storageFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return storageFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (storageFailure != null) {
      return storageFailure(this);
    }
    return orElse();
  }
}

abstract class StorageFailure implements SymptomManagementFailure {
  const factory StorageFailure() = _$StorageFailureImpl;
}

/// @nodoc
abstract class _$$ParseFailureImplCopyWith<$Res> {
  factory _$$ParseFailureImplCopyWith(
          _$ParseFailureImpl value, $Res Function(_$ParseFailureImpl) then) =
      __$$ParseFailureImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ParseFailureImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$ParseFailureImpl>
    implements _$$ParseFailureImplCopyWith<$Res> {
  __$$ParseFailureImplCopyWithImpl(
      _$ParseFailureImpl _value, $Res Function(_$ParseFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ParseFailureImpl implements ParseFailure {
  const _$ParseFailureImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.parseFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ParseFailureImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return parseFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return parseFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (parseFailure != null) {
      return parseFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return parseFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return parseFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (parseFailure != null) {
      return parseFailure(this);
    }
    return orElse();
  }
}

abstract class ParseFailure implements SymptomManagementFailure {
  const factory ParseFailure() = _$ParseFailureImpl;
}

/// @nodoc
abstract class _$$UnauthenticatedImplCopyWith<$Res> {
  factory _$$UnauthenticatedImplCopyWith(_$UnauthenticatedImpl value,
          $Res Function(_$UnauthenticatedImpl) then) =
      __$$UnauthenticatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnauthenticatedImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$UnauthenticatedImpl>
    implements _$$UnauthenticatedImplCopyWith<$Res> {
  __$$UnauthenticatedImplCopyWithImpl(
      _$UnauthenticatedImpl _value, $Res Function(_$UnauthenticatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnauthenticatedImpl implements Unauthenticated {
  const _$UnauthenticatedImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.unauthenticated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnauthenticatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return unauthenticated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return unauthenticated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return unauthenticated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return unauthenticated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (unauthenticated != null) {
      return unauthenticated(this);
    }
    return orElse();
  }
}

abstract class Unauthenticated implements SymptomManagementFailure {
  const factory Unauthenticated() = _$UnauthenticatedImpl;
}

/// @nodoc
abstract class _$$UnexpectedImplCopyWith<$Res> {
  factory _$$UnexpectedImplCopyWith(
          _$UnexpectedImpl value, $Res Function(_$UnexpectedImpl) then) =
      __$$UnexpectedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedImplCopyWithImpl<$Res>
    extends _$SymptomManagementFailureCopyWithImpl<$Res, _$UnexpectedImpl>
    implements _$$UnexpectedImplCopyWith<$Res> {
  __$$UnexpectedImplCopyWithImpl(
      _$UnexpectedImpl _value, $Res Function(_$UnexpectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomManagementFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnexpectedImpl implements Unexpected {
  const _$UnexpectedImpl();

  @override
  String toString() {
    return 'SymptomManagementFailure.unexpected()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class Unexpected implements SymptomManagementFailure {
  const factory Unexpected() = _$UnexpectedImpl;
}
