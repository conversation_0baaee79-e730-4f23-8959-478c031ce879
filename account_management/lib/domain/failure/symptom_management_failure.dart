import 'package:freezed_annotation/freezed_annotation.dart';
part 'symptom_management_failure.freezed.dart';

@freezed
class SymptomManagementFailure with _$SymptomManagementFailure {
  const factory SymptomManagementFailure.networkFailure() = NetworkFailure;
  const factory SymptomManagementFailure.cacheFailure() = CacheFailure;
  const factory SymptomManagementFailure.syncFailure() = SyncFailure;
  const factory SymptomManagementFailure.downloadFailure() = DownloadFailure;
  const factory SymptomManagementFailure.storageFailure() = StorageFailure;
  const factory SymptomManagementFailure.parseFailure() = ParseFailure;
  const factory SymptomManagementFailure.unauthenticated() = Unauthenticated;
  const factory SymptomManagementFailure.unexpected() = Unexpected;
}
