// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'symptom_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SymptomModel _$SymptomModelFromJson(Map<String, dynamic> json) => SymptomModel(
      name: json['name'] as String,
      category: json['category'] as String,
      iconUrl: json['iconUrl'] as String?,
      localIconPath: json['localIconPath'] as String?,
      colorCode: json['colorCode'] as String?,
    );

Map<String, dynamic> _$SymptomModelToJson(SymptomModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'category': instance.category,
      'iconUrl': instance.iconUrl,
      'localIconPath': instance.localIconPath,
      'colorCode': instance.colorCode,
    };
