import 'package:account_management/domain/model/symptom_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive_ce/hive.dart';
import 'health_data.dart';

part 'period_tracking_model.g.dart';

@JsonSerializable(explicitToJson: true)
class PeriodTrackingModel {
  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  DateTime? date;
  List<SymptomModel>? symptoms;
  int? painLevel;
  int? flowLevel;
  bool? isPeriodDate;
  bool? isOvulationDate;

  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  DateTime? lastUpdated;

  PeriodTrackingModel({
    this.date,
    this.symptoms,
    this.painLevel,
    this.flowLevel,
    this.isPeriodDate,
    this.isOvulationDate,
    this.lastUpdated,
  });

  factory PeriodTrackingModel.empty() => PeriodTrackingModel(
        date: null,
        symptoms: [],
        painLevel: 0,
        flowLevel: 0,
        lastUpdated: null,
      );

  factory PeriodTrackingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PeriodTrackingModel.fromJson(data);
  }

  factory PeriodTrackingModel.fromJson(Map<String, dynamic> json) =>
      _$PeriodTrackingModelFromJson(json);

  Map<String, dynamic> toJson() => _$PeriodTrackingModelToJson(this);

  PeriodTrackingModel copyWith({
    DateTime? date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
    bool? isPeriodDate,
    bool? isOvulationDate,
    DateTime? lastUpdated,
  }) {
    return PeriodTrackingModel(
      date: date ?? this.date,
      symptoms: symptoms ?? this.symptoms,
      painLevel: painLevel ?? this.painLevel,
      flowLevel: flowLevel ?? this.flowLevel,
      isPeriodDate: isPeriodDate ?? this.isPeriodDate,
      isOvulationDate: isOvulationDate ?? this.isOvulationDate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

@HiveType(typeId: 1)
@JsonSerializable(explicitToJson: true)
class LocalPeriodTrackingModel extends HiveObject {
  @HiveField(0)
  late DateTime date;

  @HiveField(1)
  int? flowLevel;

  @HiveField(2)
  int? painLevel;

  @HiveField(3)
  List<String>? symptoms;

  @HiveField(4)
  bool synced;

  @HiveField(5)
  DateTime? lastModified;

  @HiveField(6)
  int version;

  LocalPeriodTrackingModel({
    required this.date,
    this.flowLevel,
    this.painLevel,
    this.symptoms,
    this.synced = false,
    this.lastModified,
    this.version = 0,
  });

  bool shouldOverwrite(LocalPeriodTrackingModel other) {
    // Prioritize based on:
    // 1. Version number (primary)
    // 2. Last modification time (secondary)
    if (version != other.version) {
      return version > other.version;
    }
    return (lastModified ?? DateTime(0))
        .isAfter(other.lastModified ?? DateTime(0));
  }

  // Convert from remote PeriodTrackingModel to local model
  factory LocalPeriodTrackingModel.fromRemote(PeriodTrackingModel remoteModel) {
    return LocalPeriodTrackingModel(
      date: remoteModel.date!,
      flowLevel: remoteModel.flowLevel,
      painLevel: remoteModel.painLevel,
      symptoms: remoteModel.symptoms?.map((s) => s.name).toList(),
      synced: true,
      lastModified: DateTime.now(),
    );
  }

  // Convert local model back to remote model
  PeriodTrackingModel toRemoteModel() {
    return PeriodTrackingModel(
      date: date,
      flowLevel: flowLevel,
      painLevel: painLevel,
      symptoms: symptoms
          ?.map((s) => SymptomModel(
                name: s,
                category: 'General', // Default category for legacy symptoms
                iconUrl: null, // No icon URL for legacy symptoms
              ))
          .toList(),
    );
  }

  factory LocalPeriodTrackingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return LocalPeriodTrackingModel.fromJson(data);
  }

  factory LocalPeriodTrackingModel.fromJson(Map<String, dynamic> json) =>
      _$LocalPeriodTrackingModelFromJson(json);

  Map<String, dynamic> toJson() => _$LocalPeriodTrackingModelToJson(this);
}
