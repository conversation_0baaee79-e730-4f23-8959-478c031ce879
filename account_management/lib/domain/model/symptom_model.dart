import 'package:json_annotation/json_annotation.dart';

part 'symptom_model.g.dart';

@JsonSerializable()
class SymptomModel {
  final String name;
  final String category;
  final String? iconUrl; // Made nullable
  final String? localIconPath; // For cached local icon path
  final String? colorCode; // Color code for category styling (e.g., "#584294")

  SymptomModel({
    required this.name,
    required this.category,
    this.iconUrl, // Now optional
    this.localIconPath,
    this.colorCode, // Now optional
  });

  factory SymptomModel.fromJson(Map<String, dynamic> json) =>
      _$SymptomModelFromJson(json);

  Map<String, dynamic> toJson() => _$SymptomModelToJson(this);

  // Helper method to create a copy with updated local icon path
  SymptomModel copyWith({
    String? name,
    String? category,
    String? iconUrl,
    String? localIconPath,
    String? colorCode,
  }) {
    return SymptomModel(
      name: name ?? this.name,
      category: category ?? this.category,
      iconUrl: iconUrl ?? this.iconUrl,
      localIconPath: localIconPath ?? this.localIconPath,
      colorCode: colorCode ?? this.colorCode,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SymptomModel &&
        other.name == name &&
        other.category == category &&
        other.iconUrl == iconUrl &&
        other.localIconPath == localIconPath &&
        other.colorCode == colorCode;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        category.hashCode ^
        iconUrl.hashCode ^
        localIconPath.hashCode ^
        colorCode.hashCode;
  }
}
