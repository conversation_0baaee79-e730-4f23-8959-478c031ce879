import 'package:fpdart/fpdart.dart';
import '../failure/period_tracking_failure.dart';
import '../model/period_reminder_settings.dart';

abstract class PeriodReminderFacade {
  // Reminder settings methods
  Future<Either<PeriodTrackingFailure, PeriodReminderSettings>>
      getReminderSettings();
  
  Future<Either<PeriodTrackingFailure, Unit>> saveReminderSettings(
      PeriodReminderSettings settings);
  
  Future<Either<PeriodTrackingFailure, Unit>> scheduleNotificationsForSettings(
      PeriodReminderSettings settings);
  
  Future<Either<PeriodTrackingFailure, Unit>> cancelAllPeriodNotifications();

  // Initialization method
  Future<Either<PeriodTrackingFailure, Unit>> initializePeriodReminders();
  
  // Helper method to reschedule notifications after period data changes
  Future<void> rescheduleNotificationsAfterPeriodUpdate();
}
