import 'package:fpdart/fpdart.dart';
import '../failure/symptom_management_failure.dart';
import '../model/symptom_model.dart';

abstract class SymptomManagementFacade {
  /// Watches all symptoms grouped by category
  /// Returns a stream of Either<SymptomManagementFailure, Map<String, List<SymptomModel>>>
  /// where the key is the category name and value is the list of symptoms in that category
  Stream<Either<SymptomManagementFailure, Map<String, List<SymptomModel>>>>
      watchSymptomsByCategory();

  /// Fetches all symptoms from Firebase and updates local cache
  /// This method is called on app startup to sync with remote data
  Future<Either<SymptomManagementFailure, Unit>> syncSymptomsFromRemote();

  /// Downloads and caches an icon locally
  /// Returns the local file path if successful
  Future<Either<SymptomManagementFailure, String>> downloadAndCacheIcon(
      String iconUrl, String symptomName);

  /// Gets the local icon path for a symptom
  /// Returns null if icon is not cached locally
  Future<String?> getLocalIconPath(String symptomName);

  /// Checks if local cache needs to be updated
  /// Compares local cache timestamp with remote data
  Future<bool> shouldSyncWithRemote();

  /// Gets all symptoms from local cache
  Future<Either<SymptomManagementFailure, Map<String, List<SymptomModel>>>>
      getLocalSymptoms();

  /// Saves symptoms to local cache
  Future<Either<SymptomManagementFailure, Unit>> saveToLocalCache(
      Map<String, List<SymptomModel>> symptomsByCategory);

  /// Clears local cache (useful for testing or reset)
  Future<Either<SymptomManagementFailure, Unit>> clearLocalCache();
}
