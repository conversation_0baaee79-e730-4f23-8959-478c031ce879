

import 'package:account_management/domain/facade/mestrual_cycle_facade.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';

import '../domain/failure/period_tracking_failure.dart';
import '../domain/model/health_data.dart';
import 'menstrual_cycle_data_model.dart';

@LazySingleton(as: MenstrualCycleFacade)
class MestrualCycleRepository implements MenstrualCycleFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;


  // Cycle configuration
  int _cycleLength = 28;
  int _periodLength = 6;

  @override
  Stream<Either<PeriodTrackingFailure, MenstrualCycleData>>
  getMenstrualCycle() async* {
    try {
      // Get initial data
      final initialDataResult = await getInitialMenstrualCycleData();
      yield initialDataResult;

      // For now, just yield the initial data
      // In a full implementation, you might want to listen to changes
    } catch (e) {
      yield Left(PeriodTrackingFailure.unexpected());
    }
  }

  // Helper method to group period dates into separate menstrual cycles
  List<List<DateTime>> _groupDatesIntoCycles(Set<DateTime> selectedDates) {
    final sortedDates = selectedDates.toList()..sort();
    final cycles = <List<DateTime>>[];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedDates.length; i++) {
      if (currentCycle.isEmpty) {
        // Start new cycle
        currentCycle.add(sortedDates[i]);
      } else {
        final daysSinceLastDate =
            sortedDates[i].difference(currentCycle.last).inDays;

        // If gap is more than 7 days, start a new cycle (irregular period)
        // Normal period flow is typically 3-7 days, so 7+ day gap indicates new cycle
        if (daysSinceLastDate > 7) {
          // Finish current cycle and start new one
          cycles.add(List.from(currentCycle));
          currentCycle = [sortedDates[i]];
        } else {
          // Continue current cycle (consecutive or close dates)
          currentCycle.add(sortedDates[i]);
        }
      }
    }

    // Add the last cycle
    if (currentCycle.isNotEmpty) {
      cycles.add(currentCycle);
    }

    return cycles;
  }

  @override
  Future<Either<PeriodTrackingFailure, MenstrualCycleData>>
  getInitialMenstrualCycleData() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return Left(PeriodTrackingFailure.notFound());

      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) return Left(PeriodTrackingFailure.notFound());

      final userData = userDoc.data();
      if (userData == null || !userData.containsKey('healthData')) {
        return Left(PeriodTrackingFailure.notFound());
      }

      HealthDataModel healthData =
      HealthDataModel.fromJson(userData['healthData']);

      final lastPeriodDate = healthData.lastPeriodDate ?? DateTime.now();
      _cycleLength = healthData.cycleLength ?? 28;
      _periodLength = healthData.periodLength ?? 6;

      final DateTime now = DateTime.now();
      final int currentCycleDay =
          now.difference(lastPeriodDate).inDays % _cycleLength + 1;

      return Right(MenstrualCycleData(
        currentCycleDay: currentCycleDay,
        periodDays: _periodLength,
        cycleLength: _cycleLength,
        ovulationDayStart: 14,
        ovulationDaysLength: 7,
      ));
    } catch (e) {
      return Left(PeriodTrackingFailure.unexpected());
    }
  }
}