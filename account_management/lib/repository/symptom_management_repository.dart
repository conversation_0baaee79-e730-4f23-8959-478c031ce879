import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import '../domain/facade/symptom_management_facade.dart';
import '../domain/failure/symptom_management_failure.dart';
import '../domain/model/symptom_model.dart';

@LazySingleton(as: SymptomManagementFacade)
class SymptomManagementRepository implements SymptomManagementFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  static const String _localCacheKey = 'symptoms_cache';
  static const String _lastSyncKey = 'symptoms_last_sync';
  static const String _iconsDirectoryName = 'symptom_icons';

  @override
  Stream<Either<SymptomManagementFailure, Map<String, List<SymptomModel>>>>
      watchSymptomsByCategory() {
    try {
      return _firestore
          .collection('symptoms_master')
          .snapshots()
          .asyncMap((snapshot) async {
        try {
          final Map<String, List<SymptomModel>> symptomsByCategory = {};

          for (final doc in snapshot.docs) {
            final data = doc.data();
            final symptoms = (data['symptoms'] as List<dynamic>?)
                    ?.map((symptomData) => SymptomModel.fromJson(
                        symptomData as Map<String, dynamic>))
                    .toList() ??
                [];

            if (symptoms.isNotEmpty) {
              symptomsByCategory[doc.id] = symptoms;
            }
          }

          // Update local cache
          await saveToLocalCache(symptomsByCategory);

          // Download missing icons
          await _downloadMissingIcons(symptomsByCategory);

          return Right(symptomsByCategory);
        } catch (e) {
          return const Left(SymptomManagementFailure.parseFailure());
        }
      });
    } catch (e) {
      return Stream.value(
          const Left(SymptomManagementFailure.networkFailure()));
    }
  }

  @override
  Future<Either<SymptomManagementFailure, Unit>>
      syncSymptomsFromRemote() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(SymptomManagementFailure.unauthenticated());
      }

      final snapshot = await _firestore.collection('symptoms_master').get();
      final Map<String, List<SymptomModel>> symptomsByCategory = {};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final symptoms = (data['symptoms'] as List<dynamic>?)
                ?.map((symptomData) =>
                    SymptomModel.fromJson(symptomData as Map<String, dynamic>))
                .toList() ??
            [];

        if (symptoms.isNotEmpty) {
          symptomsByCategory[doc.id] = symptoms;
        }
      }

      // Save to local cache
      final cacheResult = await saveToLocalCache(symptomsByCategory);

      // Handle cache result - if it failed, return the failure
      cacheResult.mapBoth(
        onLeft: (failure) => failure, // This will be returned if cache fails
        onRight: (_) => unit, // Continue if cache succeeds
      );

      // Download missing icons
      await _downloadMissingIcons(symptomsByCategory);

      // Update last sync timestamp
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);

      return const Right(unit);
    } catch (e) {
      return const Left(SymptomManagementFailure.syncFailure());
    }
  }

  @override
  Future<Either<SymptomManagementFailure, String>> downloadAndCacheIcon(
      String iconUrl, String symptomName) async {
    try {
      final response = await http.get(Uri.parse(iconUrl));
      if (response.statusCode != 200) {
        return const Left(SymptomManagementFailure.downloadFailure());
      }

      final directory = await getApplicationDocumentsDirectory();
      final iconsDir = Directory('${directory.path}/$_iconsDirectoryName');
      if (!await iconsDir.exists()) {
        await iconsDir.create(recursive: true);
      }

      final fileName = '${symptomName.toLowerCase().replaceAll(' ', '_')}.svg';
      final file = File('${iconsDir.path}/$fileName');
      await file.writeAsBytes(response.bodyBytes);

      return Right(file.path);
    } catch (e) {
      return const Left(SymptomManagementFailure.downloadFailure());
    }
  }

  @override
  Future<String?> getLocalIconPath(String symptomName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${symptomName.toLowerCase().replaceAll(' ', '_')}.svg';
      final file = File('${directory.path}/$_iconsDirectoryName/$fileName');

      if (await file.exists()) {
        return file.path;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> shouldSyncWithRemote() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getInt(_lastSyncKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // Sync if last sync was more than 24 hours ago
      const twentyFourHours = 24 * 60 * 60 * 1000;
      return (now - lastSync) > twentyFourHours;
    } catch (e) {
      return true; // Default to sync if we can't determine
    }
  }

  @override
  Future<Either<SymptomManagementFailure, Map<String, List<SymptomModel>>>>
      getLocalSymptoms() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_localCacheKey);

      if (cachedData == null) {
        return const Right({});
      }

      final Map<String, dynamic> decodedData = jsonDecode(cachedData);
      final Map<String, List<SymptomModel>> symptomsByCategory = {};

      decodedData.forEach((category, symptoms) {
        symptomsByCategory[category] = (symptoms as List<dynamic>)
            .map((symptomData) =>
                SymptomModel.fromJson(symptomData as Map<String, dynamic>))
            .toList();
      });

      return Right(symptomsByCategory);
    } catch (e) {
      return const Left(SymptomManagementFailure.cacheFailure());
    }
  }

  @override
  Future<Either<SymptomManagementFailure, Unit>> saveToLocalCache(
      Map<String, List<SymptomModel>> symptomsByCategory) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final Map<String, dynamic> dataToCache = {};
      symptomsByCategory.forEach((category, symptoms) {
        dataToCache[category] =
            symptoms.map((symptom) => symptom.toJson()).toList();
      });

      await prefs.setString(_localCacheKey, jsonEncode(dataToCache));
      return const Right(unit);
    } catch (e) {
      return const Left(SymptomManagementFailure.cacheFailure());
    }
  }

  @override
  Future<Either<SymptomManagementFailure, Unit>> clearLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_localCacheKey);
      await prefs.remove(_lastSyncKey);

      // Also clear icon cache
      final directory = await getApplicationDocumentsDirectory();
      final iconsDir = Directory('${directory.path}/$_iconsDirectoryName');
      if (await iconsDir.exists()) {
        await iconsDir.delete(recursive: true);
      }

      return const Right(unit);
    } catch (e) {
      return const Left(SymptomManagementFailure.cacheFailure());
    }
  }

  // Helper method to download missing icons
  Future<void> _downloadMissingIcons(
      Map<String, List<SymptomModel>> symptomsByCategory) async {
    for (final symptoms in symptomsByCategory.values) {
      for (final symptom in symptoms) {
        // Only try to download if iconUrl is not null
        if (symptom.iconUrl != null && symptom.iconUrl!.isNotEmpty) {
          final localPath = await getLocalIconPath(symptom.name);
          if (localPath == null) {
            // Icon not cached, download it
            await downloadAndCacheIcon(symptom.iconUrl!, symptom.name);
          }
        }
      }
    }
  }
}
