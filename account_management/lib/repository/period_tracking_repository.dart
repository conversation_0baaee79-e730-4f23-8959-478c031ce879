import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import '../domain/facade/period_tracking_facade.dart';
import '../domain/facade/health_data_facade.dart';
import '../domain/failure/period_tracking_failure.dart';
import '../domain/model/period_tracking_model.dart';
import '../domain/model/symptom_model.dart';

@LazySingleton(as: PeriodTrackingFacade)
class PeriodTrackingRepository implements PeriodTrackingFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final HealthDataFacade _healthDataFacade;

  PeriodTrackingRepository(this._healthDataFacade);

  @override
  Stream<
      Either<PeriodTrackingFailure,
          Map<String, Map<String, PeriodTrackingModel>>>> watchYearData(
      int year) {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Stream.value(
            const Left(PeriodTrackingFailure.unauthenticated()));
      }

      // Watch all months for the given year
      return _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year.toString())
          .collection('months')
          .snapshots()
          .map<
              Either<PeriodTrackingFailure,
                  Map<String, Map<String, PeriodTrackingModel>>>>((snapshot) {
        try {
          final yearData = <String, Map<String, PeriodTrackingModel>>{};

          for (final doc in snapshot.docs) {
            final monthKey = doc.id; // e.g., "2025_01"
            final monthData = doc.data();
            final daysData = monthData['days'] as Map<String, dynamic>? ?? {};

            final monthPeriodData = <String, PeriodTrackingModel>{};

            for (final dayEntry in daysData.entries) {
              final dayKey = dayEntry.key; // e.g., "01"
              final dayData = dayEntry.value as Map<String, dynamic>;

              // Convert day data to PeriodTrackingModel
              final periodModel = PeriodTrackingModel.fromJson(dayData);

              monthPeriodData[dayKey] = periodModel;
            }

            if (monthPeriodData.isNotEmpty) {
              yearData[monthKey] = monthPeriodData;
            }
          }

          return Right(yearData);
        } catch (e) {
          return const Left(PeriodTrackingFailure.unexpected());
        }
      }).handleError((error) {
        return const Left(PeriodTrackingFailure.getPeriodTrackingsFailure());
      });
    } catch (e) {
      return Stream.value(const Left(PeriodTrackingFailure.unexpected()));
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> selectPeriodDates(
      Set<DateTime> selectedDates) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Group dates by month for efficient updates
      final Map<String, Map<String, Set<String>>> monthlyUpdates = {};

      for (final date in selectedDates) {
        final year = date.year.toString();
        final monthKey =
            '${date.year}_${date.month.toString().padLeft(2, '0')}';
        final dayKey = date.day.toString().padLeft(2, '0');

        monthlyUpdates[year] ??= {};
        monthlyUpdates[year]![monthKey] ??= {};
        monthlyUpdates[year]![monthKey]!.add(dayKey);
      }

      // Update each month document
      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // First, read the existing document to preserve data
          final existingDoc = await monthDocRef.get();
          final existingData = existingDoc.exists
              ? existingDoc.data() as Map<String, dynamic>?
              : null;
          final existingDays =
              existingData?['days'] as Map<String, dynamic>? ?? {};

          // Create the complete days structure
          final updatedDays = Map<String, dynamic>.from(existingDays);

          for (final dayKey in dayKeys) {
            final dayNum = int.parse(dayKey);
            final monthParts = monthKey.split('_');
            final year = int.parse(monthParts[0]);
            final month = int.parse(monthParts[1]);

            // Get existing day data or create new
            final existingDayData =
                updatedDays[dayKey] as Map<String, dynamic>? ?? {};

            // Update only the necessary fields, preserving others
            updatedDays[dayKey] = {
              ...existingDayData,
              'isPeriodDate': true,
              'lastUpdated': FieldValue.serverTimestamp(),
              'date': existingDayData['date'] ??
                  Timestamp.fromDate(DateTime(year, month, dayNum)),
            };
          }

          // Check if document exists and use appropriate operation
          final docExists = existingDoc.exists;
          if (docExists) {
            batch.update(
              monthDocRef,
              {'days': updatedDays},
            );
          } else {
            batch.set(
              monthDocRef,
              {'days': updatedDays},
              SetOptions(merge: true),
            );
          }
        }
      }

      // Commit the batch
      await batch.commit();

      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> deselectPeriodDates(
      Set<DateTime> datesToDeselect) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Group dates by month for efficient updates
      final Map<String, Map<String, Set<String>>> monthlyUpdates = {};

      for (final date in datesToDeselect) {
        final year = date.year.toString();
        final monthKey =
            '${date.year}_${date.month.toString().padLeft(2, '0')}';
        final dayKey = date.day.toString().padLeft(2, '0');

        monthlyUpdates[year] ??= {};
        monthlyUpdates[year]![monthKey] ??= {};
        monthlyUpdates[year]![monthKey]!.add(dayKey);
      }

      // Update each month document
      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // Check if document exists first
          final docSnapshot = await monthDocRef.get();
          final docExists = docSnapshot.exists;

          // Create update map for this month
          final Map<String, dynamic> updates = {};
          for (final dayKey in dayKeys) {
            // Set isPeriodDate to false, preserving other data
            updates['days.$dayKey.isPeriodDate'] = false;
            updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
          }

          // Use appropriate operation based on document existence
          if (docExists) {
            batch.update(
              monthDocRef,
              updates,
            );
          } else {
            batch.set(
              monthDocRef,
              updates,
              SetOptions(merge: true),
            );
          }
        }
      }

      // Commit the batch
      await batch.commit();

      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> calculateAndSaveOvulationDates(
      Set<DateTime> periodDates) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Get user's health data for cycle and period length
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28; // Default cycle length
      int periodLength = 5; // Default period length

      healthDataResult.mapBoth(
        onLeft: (failure) {
          // Use defaults if health data can't be retrieved
        },
        onRight: (healthData) {
          if (healthData != null) {
            cycleLength = healthData.cycleLength ?? 28;
            periodLength = healthData.periodLength ?? 5;
          }
        },
      );

      // Calculate potential ovulation range for the period dates (more targeted approach)
      final potentialOvulationRange =
          _calculatePotentialOvulationRange(periodDates, cycleLength);

      // Get existing ovulation dates only in the calculated range
      final existingOvulationDates = await _getExistingOvulationDatesInRange(
          potentialOvulationRange.start, potentialOvulationRange.end);

      // Calculate new ovulation dates for valid period cycles
      final newOvulationDates =
          _calculateOvulationDates(periodDates, cycleLength, periodLength);

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Step 1: Clear existing ovulation dates (set to false)
      if (existingOvulationDates.isNotEmpty) {
        await _clearOvulationDatesInBatch(batch, existingOvulationDates);
      }
      // Step 2: Set new ovulation dates (set to true)
      if (newOvulationDates.isNotEmpty) {
        await _setOvulationDatesInBatch(batch, newOvulationDates);
      }

      await batch.commit();
      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>>
      calculateOvulationForAffectedCycles({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
  }) async {
    try {
     debugPrint('=== AFFECTED CYCLES CALCULATION START ===');
     debugPrint('Newly Selected: $newlySelected');
     debugPrint('Newly Deselected: $newlyDeselected');

      final user = _auth.currentUser;
      if (user == null) {
       debugPrint('ERROR: User not authenticated');
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Get user's health data for cycle and period length
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28; // Default cycle length
      int periodLength = 5; // Default period length

      healthDataResult.mapBoth(
        onLeft: (failure) {
         debugPrint('Using default health data due to failure: $failure');
        },
        onRight: (healthData) {
          if (healthData != null) {
            cycleLength = healthData.cycleLength ?? 28;
            periodLength = healthData.periodLength ?? 5;
           debugPrint(
                'Using user health data - Cycle: $cycleLength, Period: $periodLength');
          } else {
           debugPrint(
                'Using default health data - Cycle: $cycleLength, Period: $periodLength');
          }
        },
      );

      // Get all existing period dates from database
     debugPrint('Fetching all existing period dates...');
      final allExistingDates = await _getAllExistingPeriodDates();
     debugPrint('All existing period dates: $allExistingDates');

      // Calculate affected cycle dates
     debugPrint('Calculating affected cycle dates...');
      final affectedDates = _getAffectedCycleDates(
        newlySelected: newlySelected,
        newlyDeselected: newlyDeselected,
        allExistingDates: allExistingDates,
      );
     debugPrint('Affected cycle dates: $affectedDates');

      if (affectedDates.isEmpty) {
       debugPrint('No affected dates found, returning early');
        return const Right(unit);
      }

      // Calculate ovulation for affected cycles only
     debugPrint('Calculating ovulation dates for affected cycles...');
      final newOvulationDates =
          _calculateOvulationDates(affectedDates, cycleLength, periodLength);
     debugPrint('New ovulation dates calculated: $newOvulationDates');

      // Clear existing ovulation dates that could be calculated from affected cycles
     debugPrint('Calculating ovulation dates to clear...');
      final existingOvulationDates =
          await _getOvulationDatesForAffectedCycles(affectedDates, cycleLength);
     debugPrint('Existing ovulation dates to clear: $existingOvulationDates');

     debugPrint('Starting batch operations...');
      final batch = _firestore.batch();

      if (existingOvulationDates.isNotEmpty) {
       debugPrint(
            'Clearing ${existingOvulationDates.length} existing ovulation dates...');
        await _clearOvulationDatesInBatch(batch, existingOvulationDates);
       debugPrint('Existing ovulation dates cleared in batch');
      }

      if (newOvulationDates.isNotEmpty) {
       debugPrint('Setting ${newOvulationDates.length} new ovulation dates...');
        await _setOvulationDatesInBatch(batch, newOvulationDates);
       debugPrint('New ovulation dates set in batch');
      }

     debugPrint('Committing batch...');
      await batch.commit();
     debugPrint('Batch committed successfully');
     debugPrint('=== AFFECTED CYCLES CALCULATION END ===');
      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> removeOvulationDatesForCycles(
      Set<DateTime> periodDatesToRemove) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Get user's health data for cycle length
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28; // Default cycle length

      healthDataResult.mapBoth(
        onLeft: (failure) {
          // Use default if health data can't be retrieved
        },
        onRight: (healthData) {
          if (healthData != null) {
            cycleLength = healthData.cycleLength ?? 28;
          }
        },
      );

      // Calculate the potential ovulation range for the deselected period dates
      final potentialOvulationRange =
          _calculatePotentialOvulationRange(periodDatesToRemove, cycleLength);

      // Get existing ovulation dates in that range
      final ovulationDatesToRemove = await _getExistingOvulationDatesInRange(
          potentialOvulationRange.start, potentialOvulationRange.end);

      if (ovulationDatesToRemove.isEmpty) {
        return const Right(unit); // No ovulation dates to remove
      }

      // Group ovulation dates by month for efficient updates
      final Map<String, Map<String, Set<String>>> monthlyUpdates = {};

      for (final date in ovulationDatesToRemove) {
        final year = date.year.toString();
        final monthKey =
            '${date.year}_${date.month.toString().padLeft(2, '0')}';
        final dayKey = date.day.toString().padLeft(2, '0');

        monthlyUpdates[year] ??= {};
        monthlyUpdates[year]![monthKey] ??= {};
        monthlyUpdates[year]![monthKey]!.add(dayKey);
      }

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Update each month document
      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // Check if document exists first
          final docSnapshot = await monthDocRef.get();
          final docExists = docSnapshot.exists;

          // Create update map for ovulation dates
          final Map<String, dynamic> updates = {};
          for (final dayKey in dayKeys) {
            updates['days.$dayKey.isOvulationDate'] = false;
            updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
          }

          // Use appropriate operation based on document existence
          if (docExists) {
            batch.update(monthDocRef, updates);
          } else {
            batch.set(monthDocRef, updates, SetOptions(merge: true));
          }
        }
      }

      await batch.commit();
      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  // Calculate ovulation dates for valid period cycles
  Set<DateTime> _calculateOvulationDates(
      Set<DateTime> periodDates, int cycleLength, int periodLength) {
    if (periodDates.isEmpty) return {};

    final ovulationDates = <DateTime>{};
    final periodCycles = _calculatePeriodCycles(periodDates);

    for (int i = 0; i < periodCycles.length; i++) {
      final currentCycle = periodCycles[i];

      // Conditions for valid ovulation calculation:
      // 1. Cycle must have at least 1 day
      // 2. If there's a next cycle, gap should be reasonable based on user's cycle length
      if (!_isValidCycleForOvulation(
          currentCycle, periodCycles, i, cycleLength)) {
        continue;
      }

      final cycleStartDate = currentCycle.first;
      DateTime? nextCycleStartDate;

      // Find next cycle start date
      if (i + 1 < periodCycles.length) {
        nextCycleStartDate = periodCycles[i + 1].first;
      } else {
        // For the last cycle, estimate based on user's cycle length
        nextCycleStartDate = cycleStartDate.add(Duration(days: cycleLength));
      }

      // Normalize dates to midnight to avoid timezone issues
      final normalizedCycleStart = DateTime(
          cycleStartDate.year, cycleStartDate.month, cycleStartDate.day);
      final normalizedNextCycleStart = DateTime(nextCycleStartDate.year,
          nextCycleStartDate.month, nextCycleStartDate.day);

      // Calculate ovulation window (5 days: 2 days before peak + peak + 2 days after)
      final ovulationPeakDate =
          normalizedNextCycleStart.subtract(const Duration(days: 14));

      // Only add if ovulation peak date is reasonable (not in the period cycle itself)
      final periodEndDate =
          normalizedCycleStart.add(Duration(days: periodLength));
      if (ovulationPeakDate.isAfter(periodEndDate)) {
        // Create 5-day ovulation window: 2 days before peak, peak day, 2 days after peak
        for (int j = -2; j <= 2; j++) {
          final ovulationDate = ovulationPeakDate.add(Duration(days: j));

          // Ensure ovulation date is not in the period cycle itself
          if (ovulationDate.isAfter(periodEndDate)) {
            // Normalize ovulation date to midnight
            final normalizedOvulationDate = DateTime(
                ovulationDate.year, ovulationDate.month, ovulationDate.day);
            ovulationDates.add(normalizedOvulationDate);
          }
        }
      }
    }

    return ovulationDates;
  }

  // Calculate potential ovulation date range based on user's cycle length
  ({DateTime start, DateTime end}) _calculatePotentialOvulationRange(
      Set<DateTime> periodDates, int cycleLength) {
    if (periodDates.isEmpty) {
      final now = DateTime.now();
      return (start: now, end: now);
    }

    final earliestPeriod = periodDates.reduce((a, b) => a.isBefore(b) ? a : b);
    final latestPeriod = periodDates.reduce((a, b) => a.isAfter(b) ? a : b);

    // Ovulation can occur 7 days after period start to cycle length days after period start
    final rangeStart = earliestPeriod.add(const Duration(days: 7));
    final rangeEnd = latestPeriod.add(Duration(days: cycleLength));

    return (start: rangeStart, end: rangeEnd);
  }

  // Get existing ovulation dates within a specific date range
  Future<Set<DateTime>> _getExistingOvulationDatesInRange(
      DateTime startDate, DateTime endDate) async {
    final user = _auth.currentUser;
    if (user == null) return {};

    try {
      final ovulationDates = <DateTime>{};

      // Get all years that might contain ovulation dates in the range
      final startYear = startDate.year;
      final endYear = endDate.year;

      for (int year = startYear; year <= endYear; year++) {
        final snapshot = await _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('years')
            .doc(year.toString())
            .collection('months')
            .get();

        for (final doc in snapshot.docs) {
          final monthData = doc.data();
          final daysData = monthData['days'] as Map<String, dynamic>? ?? {};

          for (final dayEntry in daysData.entries) {
            final dayData = dayEntry.value as Map<String, dynamic>;
            if (dayData['isOvulationDate'] == true) {
              final date = dayData['date'] as Timestamp?;
              if (date != null) {
                final ovulationDate = date.toDate();
                // Only include dates within the specified range
                if (ovulationDate
                        .isAfter(startDate.subtract(const Duration(days: 1))) &&
                    ovulationDate
                        .isBefore(endDate.add(const Duration(days: 1)))) {
                  ovulationDates.add(ovulationDate);
                }
              }
            }
          }
        }
      }

      return ovulationDates;
    } catch (e) {
      return {};
    }
  }

  // Clear existing ovulation dates in batch
  Future<void> _clearOvulationDatesInBatch(
      WriteBatch batch, Set<DateTime> ovulationDates) async {
   debugPrint('Clearing ${ovulationDates.length} ovulation dates');

    final monthlyUpdates = <String, Map<String, Set<String>>>{};

    for (final date in ovulationDates) {
      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      monthlyUpdates[year] ??= {};
      monthlyUpdates[year]![monthKey] ??= {};
      monthlyUpdates[year]![monthKey]!.add(dayKey);
    }

    final user = _auth.currentUser;
    if (user == null) return;

    for (final yearEntry in monthlyUpdates.entries) {
      final year = yearEntry.key;

      for (final monthEntry in yearEntry.value.entries) {
        final monthKey = monthEntry.key;
        final dayKeys = monthEntry.value;

        final monthDocRef = _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('years')
            .doc(year)
            .collection('months')
            .doc(monthKey);

        // First, read the existing document to preserve data
        final existingDoc = await monthDocRef.get();
        final existingData = existingDoc.exists ? existingDoc.data() : null;
        final existingDays =
            existingData?['days'] as Map<String, dynamic>? ?? {};

        // Create the complete days structure
        final updatedDays = Map<String, dynamic>.from(existingDays);

        for (final dayKey in dayKeys) {
          // Get existing day data or create new
          final existingDayData =
              updatedDays[dayKey] as Map<String, dynamic>? ?? {};

          // Update only the necessary fields, preserving others
          updatedDays[dayKey] = {
            ...existingDayData,
            'isOvulationDate': false,
            'lastUpdated': FieldValue.serverTimestamp(),
          };
        }

        // Check if document exists and use appropriate operation
        final docExists = existingDoc.exists;

        if (docExists) {
          batch.update(
            monthDocRef,
            {'days': updatedDays},
          );
        } else {
          batch.set(
            monthDocRef,
            {'days': updatedDays},
            SetOptions(merge: true),
          );
        }
      }
    }
  }

  // Set new ovulation dates in batch
  Future<void> _setOvulationDatesInBatch(
      WriteBatch batch, Set<DateTime> ovulationDates) async {
   debugPrint('Setting ${ovulationDates.length} new ovulation dates');

    final monthlyUpdates = <String, Map<String, Set<String>>>{};

    for (final date in ovulationDates) {
      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      monthlyUpdates[year] ??= {};
      monthlyUpdates[year]![monthKey] ??= {};
      monthlyUpdates[year]![monthKey]!.add(dayKey);
    }

    final user = _auth.currentUser;
    if (user == null) return;

    for (final yearEntry in monthlyUpdates.entries) {
      final year = yearEntry.key;

      for (final monthEntry in yearEntry.value.entries) {
        final monthKey = monthEntry.key;
        final dayKeys = monthEntry.value;

        final monthDocRef = _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('years')
            .doc(year)
            .collection('months')
            .doc(monthKey);

        // First, read the existing document to preserve data
        final existingDoc = await monthDocRef.get();
        final existingData = existingDoc.exists ? existingDoc.data() : null;
        final existingDays =
            existingData?['days'] as Map<String, dynamic>? ?? {};

        // Create the complete days structure
        final updatedDays = Map<String, dynamic>.from(existingDays);

        for (final dayKey in dayKeys) {
          final dayNum = int.parse(dayKey);
          final monthParts = monthKey.split('_');
          final yearNum = int.parse(monthParts[0]);
          final monthNum = int.parse(monthParts[1]);

          // Get existing day data or create new
          final existingDayData =
              updatedDays[dayKey] as Map<String, dynamic>? ?? {};

          // Update only the necessary fields, preserving others
          updatedDays[dayKey] = {
            ...existingDayData,
            'isOvulationDate': true,
            'lastUpdated': FieldValue.serverTimestamp(),
            'date': existingDayData['date'] ??
                Timestamp.fromDate(DateTime(yearNum, monthNum, dayNum)),
          };
        }

        // Check if document exists and use appropriate operation
        final docExists = existingDoc.exists;

        if (docExists) {
          batch.update(
            monthDocRef,
            {'days': updatedDays},
          );
        } else {
          batch.set(
            monthDocRef,
            {'days': updatedDays},
            SetOptions(merge: true),
          );
        }
      }
    }
  }

  // Calculate period cycles from period dates
  List<List<DateTime>> _calculatePeriodCycles(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return [];

    final sortedDates = periodDates.toList()..sort();
    List<List<DateTime>> cycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];

      if (currentCycle.isEmpty) {
        currentCycle.add(currentDate);
      } else {
        final lastDate = currentCycle.last;
        final daysDifference = currentDate.difference(lastDate).inDays;

        if (daysDifference <= 1) {
          // Consecutive day - add to current cycle
          currentCycle.add(currentDate);
        } else {
          // Gap found - end current cycle and start new one
          cycles.add(List.from(currentCycle));
          currentCycle = [currentDate];
        }
      }
    }

    // Add the last cycle
    if (currentCycle.isNotEmpty) {
      cycles.add(currentCycle);
    }

    return cycles;
  }

  // Check if a period cycle is valid for ovulation calculation
  bool _isValidCycleForOvulation(List<DateTime> currentCycle,
      List<List<DateTime>> allCycles, int cycleIndex, int userCycleLength) {
    // Condition 1: Cycle must have at least 1 day
    if (currentCycle.isEmpty) {
      return false;
    }

    // Condition 2: If there's a next cycle, check the gap is reasonable based on user's cycle length
    if (cycleIndex + 1 < allCycles.length) {
      final nextCycle = allCycles[cycleIndex + 1];
      final daysBetweenCycles =
          nextCycle.first.difference(currentCycle.last).inDays;

      // Gap should be within reasonable range of user's cycle length (±7 days)
      final minGap = (userCycleLength - 7).clamp(14, 50);
      final maxGap = (userCycleLength + 7).clamp(21, 50);

      if (daysBetweenCycles < minGap || daysBetweenCycles > maxGap) {
        return false;
      }
    }

    // Condition 3: For irregular periods, ensure cycle isn't too long
    final cycleDuration =
        currentCycle.last.difference(currentCycle.first).inDays + 1;

    if (cycleDuration > 10) {
      // If period lasts more than 10 days, it might be irregular data
      return false;
    }

    return true;
  }

  @override
  Future<Either<PeriodTrackingFailure, PeriodTrackingModel?>> getSymptomData({
    required DateTime date,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      final monthDocRef = _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year)
          .collection('months')
          .doc(monthKey);

      final docSnapshot = await monthDocRef.get();

      if (!docSnapshot.exists) {
        return const Right(null);
      }

      final monthData = docSnapshot.data();
      final daysData = monthData?['days'] as Map<String, dynamic>? ?? {};
      final dayData = daysData[dayKey] as Map<String, dynamic>?;

      if (dayData == null) {
        return const Right(null);
      }

      // Convert day data to PeriodTrackingModel
      final periodModel = PeriodTrackingModel.fromJson(dayData);
      return Right(periodModel);
    } catch (e) {
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> saveSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      final monthDocRef = _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year)
          .collection('months')
          .doc(monthKey);

      // Prepare the update data
      final Map<String, dynamic> updates = {};

      if (symptoms != null) {
        updates['days.$dayKey.symptoms'] =
            symptoms.map((s) => s.toJson()).toList();
      }

      if (painLevel != null) {
        updates['days.$dayKey.painLevel'] = painLevel;
      }

      if (flowLevel != null) {
        updates['days.$dayKey.flowLevel'] = flowLevel;
      }

      // Always update the timestamp and date
      updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
      updates['days.$dayKey.date'] = Timestamp.fromDate(date);

      // Check if document exists and get existing day data
      final docSnapshot = await monthDocRef.get();
      final docExists = docSnapshot.exists;

      Map<String, dynamic>? existingDayData;
      if (docExists) {
        final docData = docSnapshot.data();
        final daysData = docData?['days'] as Map<String, dynamic>?;
        existingDayData = daysData?[dayKey] as Map<String, dynamic>?;
      }

      // Prepare the day data, preserving existing period/ovulation flags
      final dayData = <String, dynamic>{};

      // Preserve existing isPeriodDate and isOvulationDate if they exist
      if (existingDayData != null) {
        if (existingDayData.containsKey('isPeriodDate')) {
          dayData['isPeriodDate'] = existingDayData['isPeriodDate'];
        }
        if (existingDayData.containsKey('isOvulationDate')) {
          dayData['isOvulationDate'] = existingDayData['isOvulationDate'];
        }
      }

      // Add the symptom updates
      dayData.addAll(
          updates.map((key, value) => MapEntry(key.split('.').last, value)));

      // Use appropriate operation based on document existence
      if (docExists) {
        await monthDocRef.update({
          'days.$dayKey': dayData,
        });
      } else {
        await monthDocRef.set({
          'days': {
            dayKey: dayData,
          }
        }, SetOptions(merge: true));
      }

      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  // Helper method to get all existing period dates
  Future<Set<DateTime>> _getAllExistingPeriodDates() async {
    final user = _auth.currentUser;
    if (user == null) return {};

    final allDates = <DateTime>{};
    final now = DateTime.now();

    // Get data for current year and previous year
    for (int year = now.year - 1; year <= now.year; year++) {
      final snapshot = await _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year.toString())
          .collection('months')
          .get();

      for (final doc in snapshot.docs) {
        final monthData = doc.data();
        final daysData = monthData['days'] as Map<String, dynamic>? ?? {};

        for (final dayEntry in daysData.entries) {
          final dayData = dayEntry.value as Map<String, dynamic>;
          if (dayData['isPeriodDate'] == true) {
            final date = dayData['date'] as Timestamp?;
            if (date != null) {
              allDates.add(date.toDate());
            }
          }
        }
      }
    }

    return allDates;
  }

  // Helper method to determine affected cycle dates
  Set<DateTime> _getAffectedCycleDates({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
    required Set<DateTime> allExistingDates,
  }) {
   debugPrint(
        'Finding affected cycles for: +${newlySelected.length} -${newlyDeselected.length} dates');

    // Combine all dates to see the complete picture
    final allDates = Set<DateTime>.from(allExistingDates);
    allDates.addAll(newlySelected);
    allDates.removeAll(newlyDeselected);

    if (allDates.isEmpty) return {};

    // Calculate all cycles
    final allCycles = _calculatePeriodCycles(allDates);
    final affectedCycleIndices = <int>{};

    // Find cycles that contain changed dates
    final changedDates = Set<DateTime>.from(newlySelected);
    changedDates.addAll(newlyDeselected);

    for (final changedDate in changedDates) {
      // Normalize the changed date to midnight for comparison
      final normalizedChangedDate =
          DateTime(changedDate.year, changedDate.month, changedDate.day);

      for (int i = 0; i < allCycles.length; i++) {
        final cycle = allCycles[i];
        bool foundInCycle = false;

        // First check: Is the changed date directly in this cycle?
        for (final cycleDate in cycle) {
          final normalizedCycleDate =
              DateTime(cycleDate.year, cycleDate.month, cycleDate.day);
          if (normalizedChangedDate == normalizedCycleDate) {
           debugPrint('DIRECT MATCH: $changedDate is in cycle $i');
            affectedCycleIndices.add(i);
            // Include neighboring cycles for context
            if (i > 0) affectedCycleIndices.add(i - 1);
            if (i < allCycles.length - 1) affectedCycleIndices.add(i + 1);
            foundInCycle = true;
            break;
          }
        }

        // If not found directly, check if it's close to this cycle (within 3 days)
        if (!foundInCycle) {
          for (final cycleDate in cycle) {
            final daysDiff = (normalizedChangedDate.difference(
                    DateTime(cycleDate.year, cycleDate.month, cycleDate.day)))
                .inDays
                .abs();
            if (daysDiff <= 3) {
             debugPrint(
                  'PROXIMITY MATCH: $changedDate near cycle $i (${daysDiff}d)');
              affectedCycleIndices.add(i);
              // Include neighboring cycles for context
              if (i > 0) affectedCycleIndices.add(i - 1);
              if (i < allCycles.length - 1) affectedCycleIndices.add(i + 1);
              break;
            }
          }
        }
      }
    }

    // Collect dates from affected cycles
    final affectedDates = <DateTime>{};
    for (final index in affectedCycleIndices) {
      if (index < allCycles.length) {
        affectedDates.addAll(allCycles[index]);
      }
    }

   debugPrint(
        'Found ${affectedDates.length} affected dates from ${affectedCycleIndices.length} cycles');
    return affectedDates;
  }

  // Helper method to get existing ovulation dates that could be calculated from affected cycles
  Future<Set<DateTime>> _getOvulationDatesForAffectedCycles(
      Set<DateTime> affectedDates, int cycleLength) async {
    if (affectedDates.isEmpty) return {};

    // Calculate what ovulation dates COULD be generated from these cycles
    final potentialOvulationDates =
        _calculateOvulationDates(affectedDates, cycleLength, 5);

    if (potentialOvulationDates.isEmpty) return {};

    // Get the date range that covers these potential ovulation dates
    final sortedOvulationDates = potentialOvulationDates.toList()..sort();
    final startDate =
        sortedOvulationDates.first.subtract(const Duration(days: 7));
    final endDate = sortedOvulationDates.last.add(const Duration(days: 7));

    // Fetch existing ovulation dates only in this targeted range
    return await _getExistingOvulationDatesInRange(startDate, endDate);
  }
}
