// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symptom_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SymptomWatcherEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchStarted,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        symptomsReceived,
    required TResult Function() syncRequested,
    required TResult Function() clearCache,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchStarted,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult? Function()? syncRequested,
    TResult? Function()? clearCache,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchStarted,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult Function()? syncRequested,
    TResult Function()? clearCache,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchStarted value) watchStarted,
    required TResult Function(_SymptomsReceived value) symptomsReceived,
    required TResult Function(_SyncRequested value) syncRequested,
    required TResult Function(_ClearCache value) clearCache,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchStarted value)? watchStarted,
    TResult? Function(_SymptomsReceived value)? symptomsReceived,
    TResult? Function(_SyncRequested value)? syncRequested,
    TResult? Function(_ClearCache value)? clearCache,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchStarted value)? watchStarted,
    TResult Function(_SymptomsReceived value)? symptomsReceived,
    TResult Function(_SyncRequested value)? syncRequested,
    TResult Function(_ClearCache value)? clearCache,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SymptomWatcherEventCopyWith<$Res> {
  factory $SymptomWatcherEventCopyWith(
          SymptomWatcherEvent value, $Res Function(SymptomWatcherEvent) then) =
      _$SymptomWatcherEventCopyWithImpl<$Res, SymptomWatcherEvent>;
}

/// @nodoc
class _$SymptomWatcherEventCopyWithImpl<$Res, $Val extends SymptomWatcherEvent>
    implements $SymptomWatcherEventCopyWith<$Res> {
  _$SymptomWatcherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WatchStartedImplCopyWith<$Res> {
  factory _$$WatchStartedImplCopyWith(
          _$WatchStartedImpl value, $Res Function(_$WatchStartedImpl) then) =
      __$$WatchStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WatchStartedImplCopyWithImpl<$Res>
    extends _$SymptomWatcherEventCopyWithImpl<$Res, _$WatchStartedImpl>
    implements _$$WatchStartedImplCopyWith<$Res> {
  __$$WatchStartedImplCopyWithImpl(
      _$WatchStartedImpl _value, $Res Function(_$WatchStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WatchStartedImpl implements _WatchStarted {
  const _$WatchStartedImpl();

  @override
  String toString() {
    return 'SymptomWatcherEvent.watchStarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WatchStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchStarted,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        symptomsReceived,
    required TResult Function() syncRequested,
    required TResult Function() clearCache,
  }) {
    return watchStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchStarted,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult? Function()? syncRequested,
    TResult? Function()? clearCache,
  }) {
    return watchStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchStarted,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult Function()? syncRequested,
    TResult Function()? clearCache,
    required TResult orElse(),
  }) {
    if (watchStarted != null) {
      return watchStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchStarted value) watchStarted,
    required TResult Function(_SymptomsReceived value) symptomsReceived,
    required TResult Function(_SyncRequested value) syncRequested,
    required TResult Function(_ClearCache value) clearCache,
  }) {
    return watchStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchStarted value)? watchStarted,
    TResult? Function(_SymptomsReceived value)? symptomsReceived,
    TResult? Function(_SyncRequested value)? syncRequested,
    TResult? Function(_ClearCache value)? clearCache,
  }) {
    return watchStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchStarted value)? watchStarted,
    TResult Function(_SymptomsReceived value)? symptomsReceived,
    TResult Function(_SyncRequested value)? syncRequested,
    TResult Function(_ClearCache value)? clearCache,
    required TResult orElse(),
  }) {
    if (watchStarted != null) {
      return watchStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchStarted implements SymptomWatcherEvent {
  const factory _WatchStarted() = _$WatchStartedImpl;
}

/// @nodoc
abstract class _$$SymptomsReceivedImplCopyWith<$Res> {
  factory _$$SymptomsReceivedImplCopyWith(_$SymptomsReceivedImpl value,
          $Res Function(_$SymptomsReceivedImpl) then) =
      __$$SymptomsReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, List<SymptomModel>> symptomsByCategory});
}

/// @nodoc
class __$$SymptomsReceivedImplCopyWithImpl<$Res>
    extends _$SymptomWatcherEventCopyWithImpl<$Res, _$SymptomsReceivedImpl>
    implements _$$SymptomsReceivedImplCopyWith<$Res> {
  __$$SymptomsReceivedImplCopyWithImpl(_$SymptomsReceivedImpl _value,
      $Res Function(_$SymptomsReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? symptomsByCategory = null,
  }) {
    return _then(_$SymptomsReceivedImpl(
      symptomsByCategory: null == symptomsByCategory
          ? _value._symptomsByCategory
          : symptomsByCategory // ignore: cast_nullable_to_non_nullable
              as Map<String, List<SymptomModel>>,
    ));
  }
}

/// @nodoc

class _$SymptomsReceivedImpl implements _SymptomsReceived {
  const _$SymptomsReceivedImpl(
      {required final Map<String, List<SymptomModel>> symptomsByCategory})
      : _symptomsByCategory = symptomsByCategory;

  final Map<String, List<SymptomModel>> _symptomsByCategory;
  @override
  Map<String, List<SymptomModel>> get symptomsByCategory {
    if (_symptomsByCategory is EqualUnmodifiableMapView)
      return _symptomsByCategory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_symptomsByCategory);
  }

  @override
  String toString() {
    return 'SymptomWatcherEvent.symptomsReceived(symptomsByCategory: $symptomsByCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SymptomsReceivedImpl &&
            const DeepCollectionEquality()
                .equals(other._symptomsByCategory, _symptomsByCategory));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_symptomsByCategory));

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SymptomsReceivedImplCopyWith<_$SymptomsReceivedImpl> get copyWith =>
      __$$SymptomsReceivedImplCopyWithImpl<_$SymptomsReceivedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchStarted,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        symptomsReceived,
    required TResult Function() syncRequested,
    required TResult Function() clearCache,
  }) {
    return symptomsReceived(symptomsByCategory);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchStarted,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult? Function()? syncRequested,
    TResult? Function()? clearCache,
  }) {
    return symptomsReceived?.call(symptomsByCategory);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchStarted,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult Function()? syncRequested,
    TResult Function()? clearCache,
    required TResult orElse(),
  }) {
    if (symptomsReceived != null) {
      return symptomsReceived(symptomsByCategory);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchStarted value) watchStarted,
    required TResult Function(_SymptomsReceived value) symptomsReceived,
    required TResult Function(_SyncRequested value) syncRequested,
    required TResult Function(_ClearCache value) clearCache,
  }) {
    return symptomsReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchStarted value)? watchStarted,
    TResult? Function(_SymptomsReceived value)? symptomsReceived,
    TResult? Function(_SyncRequested value)? syncRequested,
    TResult? Function(_ClearCache value)? clearCache,
  }) {
    return symptomsReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchStarted value)? watchStarted,
    TResult Function(_SymptomsReceived value)? symptomsReceived,
    TResult Function(_SyncRequested value)? syncRequested,
    TResult Function(_ClearCache value)? clearCache,
    required TResult orElse(),
  }) {
    if (symptomsReceived != null) {
      return symptomsReceived(this);
    }
    return orElse();
  }
}

abstract class _SymptomsReceived implements SymptomWatcherEvent {
  const factory _SymptomsReceived(
          {required final Map<String, List<SymptomModel>> symptomsByCategory}) =
      _$SymptomsReceivedImpl;

  Map<String, List<SymptomModel>> get symptomsByCategory;

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SymptomsReceivedImplCopyWith<_$SymptomsReceivedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SyncRequestedImplCopyWith<$Res> {
  factory _$$SyncRequestedImplCopyWith(
          _$SyncRequestedImpl value, $Res Function(_$SyncRequestedImpl) then) =
      __$$SyncRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SyncRequestedImplCopyWithImpl<$Res>
    extends _$SymptomWatcherEventCopyWithImpl<$Res, _$SyncRequestedImpl>
    implements _$$SyncRequestedImplCopyWith<$Res> {
  __$$SyncRequestedImplCopyWithImpl(
      _$SyncRequestedImpl _value, $Res Function(_$SyncRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SyncRequestedImpl implements _SyncRequested {
  const _$SyncRequestedImpl();

  @override
  String toString() {
    return 'SymptomWatcherEvent.syncRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SyncRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchStarted,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        symptomsReceived,
    required TResult Function() syncRequested,
    required TResult Function() clearCache,
  }) {
    return syncRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchStarted,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult? Function()? syncRequested,
    TResult? Function()? clearCache,
  }) {
    return syncRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchStarted,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult Function()? syncRequested,
    TResult Function()? clearCache,
    required TResult orElse(),
  }) {
    if (syncRequested != null) {
      return syncRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchStarted value) watchStarted,
    required TResult Function(_SymptomsReceived value) symptomsReceived,
    required TResult Function(_SyncRequested value) syncRequested,
    required TResult Function(_ClearCache value) clearCache,
  }) {
    return syncRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchStarted value)? watchStarted,
    TResult? Function(_SymptomsReceived value)? symptomsReceived,
    TResult? Function(_SyncRequested value)? syncRequested,
    TResult? Function(_ClearCache value)? clearCache,
  }) {
    return syncRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchStarted value)? watchStarted,
    TResult Function(_SymptomsReceived value)? symptomsReceived,
    TResult Function(_SyncRequested value)? syncRequested,
    TResult Function(_ClearCache value)? clearCache,
    required TResult orElse(),
  }) {
    if (syncRequested != null) {
      return syncRequested(this);
    }
    return orElse();
  }
}

abstract class _SyncRequested implements SymptomWatcherEvent {
  const factory _SyncRequested() = _$SyncRequestedImpl;
}

/// @nodoc
abstract class _$$ClearCacheImplCopyWith<$Res> {
  factory _$$ClearCacheImplCopyWith(
          _$ClearCacheImpl value, $Res Function(_$ClearCacheImpl) then) =
      __$$ClearCacheImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearCacheImplCopyWithImpl<$Res>
    extends _$SymptomWatcherEventCopyWithImpl<$Res, _$ClearCacheImpl>
    implements _$$ClearCacheImplCopyWith<$Res> {
  __$$ClearCacheImplCopyWithImpl(
      _$ClearCacheImpl _value, $Res Function(_$ClearCacheImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearCacheImpl implements _ClearCache {
  const _$ClearCacheImpl();

  @override
  String toString() {
    return 'SymptomWatcherEvent.clearCache()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearCacheImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchStarted,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        symptomsReceived,
    required TResult Function() syncRequested,
    required TResult Function() clearCache,
  }) {
    return clearCache();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchStarted,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult? Function()? syncRequested,
    TResult? Function()? clearCache,
  }) {
    return clearCache?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchStarted,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult Function()? syncRequested,
    TResult Function()? clearCache,
    required TResult orElse(),
  }) {
    if (clearCache != null) {
      return clearCache();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchStarted value) watchStarted,
    required TResult Function(_SymptomsReceived value) symptomsReceived,
    required TResult Function(_SyncRequested value) syncRequested,
    required TResult Function(_ClearCache value) clearCache,
  }) {
    return clearCache(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchStarted value)? watchStarted,
    TResult? Function(_SymptomsReceived value)? symptomsReceived,
    TResult? Function(_SyncRequested value)? syncRequested,
    TResult? Function(_ClearCache value)? clearCache,
  }) {
    return clearCache?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchStarted value)? watchStarted,
    TResult Function(_SymptomsReceived value)? symptomsReceived,
    TResult Function(_SyncRequested value)? syncRequested,
    TResult Function(_ClearCache value)? clearCache,
    required TResult orElse(),
  }) {
    if (clearCache != null) {
      return clearCache(this);
    }
    return orElse();
  }
}

abstract class _ClearCache implements SymptomWatcherEvent {
  const factory _ClearCache() = _$ClearCacheImpl;
}

/// @nodoc
mixin _$SymptomWatcherState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        loadSuccess,
    required TResult Function(SymptomManagementFailure failure) loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult? Function(SymptomManagementFailure failure)? loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult Function(SymptomManagementFailure failure)? loadFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SymptomWatcherStateCopyWith<$Res> {
  factory $SymptomWatcherStateCopyWith(
          SymptomWatcherState value, $Res Function(SymptomWatcherState) then) =
      _$SymptomWatcherStateCopyWithImpl<$Res, SymptomWatcherState>;
}

/// @nodoc
class _$SymptomWatcherStateCopyWithImpl<$Res, $Val extends SymptomWatcherState>
    implements $SymptomWatcherStateCopyWith<$Res> {
  _$SymptomWatcherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$SymptomWatcherStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'SymptomWatcherState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        loadSuccess,
    required TResult Function(SymptomManagementFailure failure) loadFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult? Function(SymptomManagementFailure failure)? loadFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult Function(SymptomManagementFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements SymptomWatcherState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$SymptomWatcherStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'SymptomWatcherState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        loadSuccess,
    required TResult Function(SymptomManagementFailure failure) loadFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult? Function(SymptomManagementFailure failure)? loadFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult Function(SymptomManagementFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements SymptomWatcherState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadSuccessImplCopyWith<$Res> {
  factory _$$LoadSuccessImplCopyWith(
          _$LoadSuccessImpl value, $Res Function(_$LoadSuccessImpl) then) =
      __$$LoadSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, List<SymptomModel>> symptomsByCategory});
}

/// @nodoc
class __$$LoadSuccessImplCopyWithImpl<$Res>
    extends _$SymptomWatcherStateCopyWithImpl<$Res, _$LoadSuccessImpl>
    implements _$$LoadSuccessImplCopyWith<$Res> {
  __$$LoadSuccessImplCopyWithImpl(
      _$LoadSuccessImpl _value, $Res Function(_$LoadSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? symptomsByCategory = null,
  }) {
    return _then(_$LoadSuccessImpl(
      symptomsByCategory: null == symptomsByCategory
          ? _value._symptomsByCategory
          : symptomsByCategory // ignore: cast_nullable_to_non_nullable
              as Map<String, List<SymptomModel>>,
    ));
  }
}

/// @nodoc

class _$LoadSuccessImpl implements _LoadSuccess {
  const _$LoadSuccessImpl(
      {required final Map<String, List<SymptomModel>> symptomsByCategory})
      : _symptomsByCategory = symptomsByCategory;

  final Map<String, List<SymptomModel>> _symptomsByCategory;
  @override
  Map<String, List<SymptomModel>> get symptomsByCategory {
    if (_symptomsByCategory is EqualUnmodifiableMapView)
      return _symptomsByCategory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_symptomsByCategory);
  }

  @override
  String toString() {
    return 'SymptomWatcherState.loadSuccess(symptomsByCategory: $symptomsByCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadSuccessImpl &&
            const DeepCollectionEquality()
                .equals(other._symptomsByCategory, _symptomsByCategory));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_symptomsByCategory));

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadSuccessImplCopyWith<_$LoadSuccessImpl> get copyWith =>
      __$$LoadSuccessImplCopyWithImpl<_$LoadSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        loadSuccess,
    required TResult Function(SymptomManagementFailure failure) loadFailure,
  }) {
    return loadSuccess(symptomsByCategory);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult? Function(SymptomManagementFailure failure)? loadFailure,
  }) {
    return loadSuccess?.call(symptomsByCategory);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult Function(SymptomManagementFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadSuccess != null) {
      return loadSuccess(symptomsByCategory);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return loadSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return loadSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadSuccess != null) {
      return loadSuccess(this);
    }
    return orElse();
  }
}

abstract class _LoadSuccess implements SymptomWatcherState {
  const factory _LoadSuccess(
          {required final Map<String, List<SymptomModel>> symptomsByCategory}) =
      _$LoadSuccessImpl;

  Map<String, List<SymptomModel>> get symptomsByCategory;

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadSuccessImplCopyWith<_$LoadSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadFailureImplCopyWith<$Res> {
  factory _$$LoadFailureImplCopyWith(
          _$LoadFailureImpl value, $Res Function(_$LoadFailureImpl) then) =
      __$$LoadFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SymptomManagementFailure failure});

  $SymptomManagementFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$LoadFailureImplCopyWithImpl<$Res>
    extends _$SymptomWatcherStateCopyWithImpl<$Res, _$LoadFailureImpl>
    implements _$$LoadFailureImplCopyWith<$Res> {
  __$$LoadFailureImplCopyWithImpl(
      _$LoadFailureImpl _value, $Res Function(_$LoadFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$LoadFailureImpl(
      failure: null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as SymptomManagementFailure,
    ));
  }

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SymptomManagementFailureCopyWith<$Res> get failure {
    return $SymptomManagementFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$LoadFailureImpl implements _LoadFailure {
  const _$LoadFailureImpl({required this.failure});

  @override
  final SymptomManagementFailure failure;

  @override
  String toString() {
    return 'SymptomWatcherState.loadFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadFailureImplCopyWith<_$LoadFailureImpl> get copyWith =>
      __$$LoadFailureImplCopyWithImpl<_$LoadFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        loadSuccess,
    required TResult Function(SymptomManagementFailure failure) loadFailure,
  }) {
    return loadFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult? Function(SymptomManagementFailure failure)? loadFailure,
  }) {
    return loadFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult Function(SymptomManagementFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadFailure != null) {
      return loadFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return loadFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return loadFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadFailure != null) {
      return loadFailure(this);
    }
    return orElse();
  }
}

abstract class _LoadFailure implements SymptomWatcherState {
  const factory _LoadFailure(
      {required final SymptomManagementFailure failure}) = _$LoadFailureImpl;

  SymptomManagementFailure get failure;

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadFailureImplCopyWith<_$LoadFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
