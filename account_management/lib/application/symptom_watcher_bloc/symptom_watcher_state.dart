part of 'symptom_watcher_bloc.dart';

@freezed
class SymptomWatcherState with _$SymptomWatcherState {
  const factory SymptomWatcherState.initial() = _Initial;
  const factory SymptomWatcherState.loading() = _Loading;
  const factory SymptomWatcherState.loadSuccess({
    required Map<String, List<SymptomModel>> symptomsByCategory,
  }) = _LoadSuccess;
  const factory SymptomWatcherState.loadFailure({
    required SymptomManagementFailure failure,
  }) = _LoadFailure;
}
