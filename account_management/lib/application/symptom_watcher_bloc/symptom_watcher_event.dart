part of 'symptom_watcher_bloc.dart';

@freezed
class SymptomWatcherEvent with _$SymptomWatcherEvent {
  const factory SymptomWatcherEvent.watchStarted() = _WatchStarted;
  const factory SymptomWatcherEvent.symptomsReceived({
    required Map<String, List<SymptomModel>> symptomsByCategory,
  }) = _SymptomsReceived;
  const factory SymptomWatcherEvent.syncRequested() = _SyncRequested;
  const factory SymptomWatcherEvent.clearCache() = _ClearCache;
}
