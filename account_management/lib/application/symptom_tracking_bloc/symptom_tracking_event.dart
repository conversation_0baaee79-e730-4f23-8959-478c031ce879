part of 'symptom_tracking_bloc.dart';

@freezed
class SymptomTrackingEvent with _$SymptomTrackingEvent {
  const factory SymptomTrackingEvent.loadSymptomData({
    required DateTime date,
  }) = _LoadSymptomData;

  const factory SymptomTrackingEvent.saveSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) = _SaveSymptomData;

  const factory SymptomTrackingEvent.dateChanged({
    required DateTime newDate,
  }) = _DateChanged;
}
