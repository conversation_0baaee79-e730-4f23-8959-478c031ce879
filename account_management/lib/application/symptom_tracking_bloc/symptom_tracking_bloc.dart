import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/symptom_model.dart';

part 'symptom_tracking_event.dart';
part 'symptom_tracking_state.dart';
part 'symptom_tracking_bloc.freezed.dart';

@injectable
class SymptomTrackingBloc
    extends Bloc<SymptomTrackingEvent, SymptomTrackingState> {
  final PeriodTrackingFacade _periodTrackingFacade;

  SymptomTrackingBloc(this._periodTrackingFacade)
      : super(SymptomTrackingState.initial(selectedDate: DateTime.now())) {
    on<SymptomTrackingEvent>((event, emit) async {
      await event.when(
        loadSymptomData: (date) => _onLoadSymptomData(date, emit),
        saveSymptomData: (date, symptoms, painLevel, flowLevel) =>
            _onSaveSymptomData(date, symptoms, painLevel, flowLevel, emit),
        dateChanged: (newDate) => _onDateChanged(newDate, emit),
      );
    });
  }

  Future<void> _onLoadSymptomData(
    DateTime date,
    Emitter<SymptomTrackingState> emit,
  ) async {
    emit(SymptomTrackingState.loading(selectedDate: date));

    final result = await _periodTrackingFacade.getSymptomData(date: date);

    result.mapBoth(
      onLeft: (failure) => emit(SymptomTrackingState.failure(
        selectedDate: date,
        failure: failure,
      )),
      onRight: (periodModel) => emit(SymptomTrackingState.loaded(
        selectedDate: date,
        symptoms: periodModel?.symptoms,
        painLevel: periodModel?.painLevel,
        flowLevel: periodModel?.flowLevel,
      )),
    );
  }

  Future<void> _onSaveSymptomData(
    DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
    Emitter<SymptomTrackingState> emit,
  ) async {
    emit(SymptomTrackingState.loading(selectedDate: date));

    final result = await _periodTrackingFacade.saveSymptomData(
      date: date,
      symptoms: symptoms,
      painLevel: painLevel,
      flowLevel: flowLevel,
    );

    result.mapBoth(
      onLeft: (failure) => emit(SymptomTrackingState.failure(
        selectedDate: date,
        failure: failure,
      )),
      onRight: (_) => emit(SymptomTrackingState.success(selectedDate: date)),
    );
  }

  Future<void> _onDateChanged(
    DateTime newDate,
    Emitter<SymptomTrackingState> emit,
  ) async {
    // When date changes, automatically load symptom data for the new date
    await _onLoadSymptomData(newDate, emit);
  }
}
