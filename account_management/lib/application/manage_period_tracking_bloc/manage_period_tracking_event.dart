part of 'manage_period_tracking_bloc.dart';

@freezed
class ManagePeriodTrackingEvent with _$ManagePeriodTrackingEvent {
  const factory ManagePeriodTrackingEvent.dateSelected(DateTime date) =
      _DateSelected;
  const factory ManagePeriodTrackingEvent.dateDeselected(DateTime date) =
      _DateDeselected;
  const factory ManagePeriodTrackingEvent.selectPeriodDates(
      Set<DateTime> selectedDates) = _SelectPeriodDates;
  const factory ManagePeriodTrackingEvent.deselectPeriodDates(
      Set<DateTime> datesToDeselect) = _DeselectPeriodDates;
  const factory ManagePeriodTrackingEvent.calculateOvulationDates(
      Set<DateTime> periodDates) = _CalculateOvulationDates;
  const factory ManagePeriodTrackingEvent.calculateOvulationForAffectedCycles({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
  }) = _CalculateOvulationForAffectedCycles;
  const factory ManagePeriodTrackingEvent.removeOvulationDatesForCycles(
      Set<DateTime> periodDatesToRemove) = _RemoveOvulationDatesForCycles;
  const factory ManagePeriodTrackingEvent.clearSelection() = _ClearSelection;
}
