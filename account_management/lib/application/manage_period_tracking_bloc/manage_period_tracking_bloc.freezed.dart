// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_period_tracking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManagePeriodTrackingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManagePeriodTrackingEventCopyWith<$Res> {
  factory $ManagePeriodTrackingEventCopyWith(ManagePeriodTrackingEvent value,
          $Res Function(ManagePeriodTrackingEvent) then) =
      _$ManagePeriodTrackingEventCopyWithImpl<$Res, ManagePeriodTrackingEvent>;
}

/// @nodoc
class _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        $Val extends ManagePeriodTrackingEvent>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  _$ManagePeriodTrackingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DateSelectedImplCopyWith<$Res> {
  factory _$$DateSelectedImplCopyWith(
          _$DateSelectedImpl value, $Res Function(_$DateSelectedImpl) then) =
      __$$DateSelectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$$DateSelectedImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res, _$DateSelectedImpl>
    implements _$$DateSelectedImplCopyWith<$Res> {
  __$$DateSelectedImplCopyWithImpl(
      _$DateSelectedImpl _value, $Res Function(_$DateSelectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$DateSelectedImpl(
      null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$DateSelectedImpl implements _DateSelected {
  const _$DateSelectedImpl(this.date);

  @override
  final DateTime date;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.dateSelected(date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateSelectedImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateSelectedImplCopyWith<_$DateSelectedImpl> get copyWith =>
      __$$DateSelectedImplCopyWithImpl<_$DateSelectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return dateSelected(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return dateSelected?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (dateSelected != null) {
      return dateSelected(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return dateSelected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return dateSelected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (dateSelected != null) {
      return dateSelected(this);
    }
    return orElse();
  }
}

abstract class _DateSelected implements ManagePeriodTrackingEvent {
  const factory _DateSelected(final DateTime date) = _$DateSelectedImpl;

  DateTime get date;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateSelectedImplCopyWith<_$DateSelectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DateDeselectedImplCopyWith<$Res> {
  factory _$$DateDeselectedImplCopyWith(_$DateDeselectedImpl value,
          $Res Function(_$DateDeselectedImpl) then) =
      __$$DateDeselectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$$DateDeselectedImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res, _$DateDeselectedImpl>
    implements _$$DateDeselectedImplCopyWith<$Res> {
  __$$DateDeselectedImplCopyWithImpl(
      _$DateDeselectedImpl _value, $Res Function(_$DateDeselectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$DateDeselectedImpl(
      null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$DateDeselectedImpl implements _DateDeselected {
  const _$DateDeselectedImpl(this.date);

  @override
  final DateTime date;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.dateDeselected(date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateDeselectedImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateDeselectedImplCopyWith<_$DateDeselectedImpl> get copyWith =>
      __$$DateDeselectedImplCopyWithImpl<_$DateDeselectedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return dateDeselected(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return dateDeselected?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (dateDeselected != null) {
      return dateDeselected(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return dateDeselected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return dateDeselected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (dateDeselected != null) {
      return dateDeselected(this);
    }
    return orElse();
  }
}

abstract class _DateDeselected implements ManagePeriodTrackingEvent {
  const factory _DateDeselected(final DateTime date) = _$DateDeselectedImpl;

  DateTime get date;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateDeselectedImplCopyWith<_$DateDeselectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectPeriodDatesImplCopyWith<$Res> {
  factory _$$SelectPeriodDatesImplCopyWith(_$SelectPeriodDatesImpl value,
          $Res Function(_$SelectPeriodDatesImpl) then) =
      __$$SelectPeriodDatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Set<DateTime> selectedDates});
}

/// @nodoc
class __$$SelectPeriodDatesImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$SelectPeriodDatesImpl>
    implements _$$SelectPeriodDatesImplCopyWith<$Res> {
  __$$SelectPeriodDatesImplCopyWithImpl(_$SelectPeriodDatesImpl _value,
      $Res Function(_$SelectPeriodDatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedDates = null,
  }) {
    return _then(_$SelectPeriodDatesImpl(
      null == selectedDates
          ? _value._selectedDates
          : selectedDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _$SelectPeriodDatesImpl implements _SelectPeriodDates {
  const _$SelectPeriodDatesImpl(final Set<DateTime> selectedDates)
      : _selectedDates = selectedDates;

  final Set<DateTime> _selectedDates;
  @override
  Set<DateTime> get selectedDates {
    if (_selectedDates is EqualUnmodifiableSetView) return _selectedDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedDates);
  }

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.selectPeriodDates(selectedDates: $selectedDates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectPeriodDatesImpl &&
            const DeepCollectionEquality()
                .equals(other._selectedDates, _selectedDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_selectedDates));

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectPeriodDatesImplCopyWith<_$SelectPeriodDatesImpl> get copyWith =>
      __$$SelectPeriodDatesImplCopyWithImpl<_$SelectPeriodDatesImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return selectPeriodDates(selectedDates);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return selectPeriodDates?.call(selectedDates);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (selectPeriodDates != null) {
      return selectPeriodDates(selectedDates);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return selectPeriodDates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return selectPeriodDates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (selectPeriodDates != null) {
      return selectPeriodDates(this);
    }
    return orElse();
  }
}

abstract class _SelectPeriodDates implements ManagePeriodTrackingEvent {
  const factory _SelectPeriodDates(final Set<DateTime> selectedDates) =
      _$SelectPeriodDatesImpl;

  Set<DateTime> get selectedDates;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelectPeriodDatesImplCopyWith<_$SelectPeriodDatesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeselectPeriodDatesImplCopyWith<$Res> {
  factory _$$DeselectPeriodDatesImplCopyWith(_$DeselectPeriodDatesImpl value,
          $Res Function(_$DeselectPeriodDatesImpl) then) =
      __$$DeselectPeriodDatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Set<DateTime> datesToDeselect});
}

/// @nodoc
class __$$DeselectPeriodDatesImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$DeselectPeriodDatesImpl>
    implements _$$DeselectPeriodDatesImplCopyWith<$Res> {
  __$$DeselectPeriodDatesImplCopyWithImpl(_$DeselectPeriodDatesImpl _value,
      $Res Function(_$DeselectPeriodDatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? datesToDeselect = null,
  }) {
    return _then(_$DeselectPeriodDatesImpl(
      null == datesToDeselect
          ? _value._datesToDeselect
          : datesToDeselect // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _$DeselectPeriodDatesImpl implements _DeselectPeriodDates {
  const _$DeselectPeriodDatesImpl(final Set<DateTime> datesToDeselect)
      : _datesToDeselect = datesToDeselect;

  final Set<DateTime> _datesToDeselect;
  @override
  Set<DateTime> get datesToDeselect {
    if (_datesToDeselect is EqualUnmodifiableSetView) return _datesToDeselect;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_datesToDeselect);
  }

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.deselectPeriodDates(datesToDeselect: $datesToDeselect)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeselectPeriodDatesImpl &&
            const DeepCollectionEquality()
                .equals(other._datesToDeselect, _datesToDeselect));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_datesToDeselect));

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeselectPeriodDatesImplCopyWith<_$DeselectPeriodDatesImpl> get copyWith =>
      __$$DeselectPeriodDatesImplCopyWithImpl<_$DeselectPeriodDatesImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return deselectPeriodDates(datesToDeselect);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return deselectPeriodDates?.call(datesToDeselect);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (deselectPeriodDates != null) {
      return deselectPeriodDates(datesToDeselect);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return deselectPeriodDates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return deselectPeriodDates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (deselectPeriodDates != null) {
      return deselectPeriodDates(this);
    }
    return orElse();
  }
}

abstract class _DeselectPeriodDates implements ManagePeriodTrackingEvent {
  const factory _DeselectPeriodDates(final Set<DateTime> datesToDeselect) =
      _$DeselectPeriodDatesImpl;

  Set<DateTime> get datesToDeselect;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeselectPeriodDatesImplCopyWith<_$DeselectPeriodDatesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CalculateOvulationDatesImplCopyWith<$Res> {
  factory _$$CalculateOvulationDatesImplCopyWith(
          _$CalculateOvulationDatesImpl value,
          $Res Function(_$CalculateOvulationDatesImpl) then) =
      __$$CalculateOvulationDatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Set<DateTime> periodDates});
}

/// @nodoc
class __$$CalculateOvulationDatesImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$CalculateOvulationDatesImpl>
    implements _$$CalculateOvulationDatesImplCopyWith<$Res> {
  __$$CalculateOvulationDatesImplCopyWithImpl(
      _$CalculateOvulationDatesImpl _value,
      $Res Function(_$CalculateOvulationDatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodDates = null,
  }) {
    return _then(_$CalculateOvulationDatesImpl(
      null == periodDates
          ? _value._periodDates
          : periodDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _$CalculateOvulationDatesImpl implements _CalculateOvulationDates {
  const _$CalculateOvulationDatesImpl(final Set<DateTime> periodDates)
      : _periodDates = periodDates;

  final Set<DateTime> _periodDates;
  @override
  Set<DateTime> get periodDates {
    if (_periodDates is EqualUnmodifiableSetView) return _periodDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_periodDates);
  }

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.calculateOvulationDates(periodDates: $periodDates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalculateOvulationDatesImpl &&
            const DeepCollectionEquality()
                .equals(other._periodDates, _periodDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_periodDates));

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalculateOvulationDatesImplCopyWith<_$CalculateOvulationDatesImpl>
      get copyWith => __$$CalculateOvulationDatesImplCopyWithImpl<
          _$CalculateOvulationDatesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return calculateOvulationDates(periodDates);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return calculateOvulationDates?.call(periodDates);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (calculateOvulationDates != null) {
      return calculateOvulationDates(periodDates);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return calculateOvulationDates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return calculateOvulationDates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (calculateOvulationDates != null) {
      return calculateOvulationDates(this);
    }
    return orElse();
  }
}

abstract class _CalculateOvulationDates implements ManagePeriodTrackingEvent {
  const factory _CalculateOvulationDates(final Set<DateTime> periodDates) =
      _$CalculateOvulationDatesImpl;

  Set<DateTime> get periodDates;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalculateOvulationDatesImplCopyWith<_$CalculateOvulationDatesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CalculateOvulationForAffectedCyclesImplCopyWith<$Res> {
  factory _$$CalculateOvulationForAffectedCyclesImplCopyWith(
          _$CalculateOvulationForAffectedCyclesImpl value,
          $Res Function(_$CalculateOvulationForAffectedCyclesImpl) then) =
      __$$CalculateOvulationForAffectedCyclesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Set<DateTime> newlySelected, Set<DateTime> newlyDeselected});
}

/// @nodoc
class __$$CalculateOvulationForAffectedCyclesImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$CalculateOvulationForAffectedCyclesImpl>
    implements _$$CalculateOvulationForAffectedCyclesImplCopyWith<$Res> {
  __$$CalculateOvulationForAffectedCyclesImplCopyWithImpl(
      _$CalculateOvulationForAffectedCyclesImpl _value,
      $Res Function(_$CalculateOvulationForAffectedCyclesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newlySelected = null,
    Object? newlyDeselected = null,
  }) {
    return _then(_$CalculateOvulationForAffectedCyclesImpl(
      newlySelected: null == newlySelected
          ? _value._newlySelected
          : newlySelected // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      newlyDeselected: null == newlyDeselected
          ? _value._newlyDeselected
          : newlyDeselected // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _$CalculateOvulationForAffectedCyclesImpl
    implements _CalculateOvulationForAffectedCycles {
  const _$CalculateOvulationForAffectedCyclesImpl(
      {required final Set<DateTime> newlySelected,
      required final Set<DateTime> newlyDeselected})
      : _newlySelected = newlySelected,
        _newlyDeselected = newlyDeselected;

  final Set<DateTime> _newlySelected;
  @override
  Set<DateTime> get newlySelected {
    if (_newlySelected is EqualUnmodifiableSetView) return _newlySelected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_newlySelected);
  }

  final Set<DateTime> _newlyDeselected;
  @override
  Set<DateTime> get newlyDeselected {
    if (_newlyDeselected is EqualUnmodifiableSetView) return _newlyDeselected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_newlyDeselected);
  }

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.calculateOvulationForAffectedCycles(newlySelected: $newlySelected, newlyDeselected: $newlyDeselected)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalculateOvulationForAffectedCyclesImpl &&
            const DeepCollectionEquality()
                .equals(other._newlySelected, _newlySelected) &&
            const DeepCollectionEquality()
                .equals(other._newlyDeselected, _newlyDeselected));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_newlySelected),
      const DeepCollectionEquality().hash(_newlyDeselected));

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalculateOvulationForAffectedCyclesImplCopyWith<
          _$CalculateOvulationForAffectedCyclesImpl>
      get copyWith => __$$CalculateOvulationForAffectedCyclesImplCopyWithImpl<
          _$CalculateOvulationForAffectedCyclesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return calculateOvulationForAffectedCycles(newlySelected, newlyDeselected);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return calculateOvulationForAffectedCycles?.call(
        newlySelected, newlyDeselected);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (calculateOvulationForAffectedCycles != null) {
      return calculateOvulationForAffectedCycles(
          newlySelected, newlyDeselected);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return calculateOvulationForAffectedCycles(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return calculateOvulationForAffectedCycles?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (calculateOvulationForAffectedCycles != null) {
      return calculateOvulationForAffectedCycles(this);
    }
    return orElse();
  }
}

abstract class _CalculateOvulationForAffectedCycles
    implements ManagePeriodTrackingEvent {
  const factory _CalculateOvulationForAffectedCycles(
          {required final Set<DateTime> newlySelected,
          required final Set<DateTime> newlyDeselected}) =
      _$CalculateOvulationForAffectedCyclesImpl;

  Set<DateTime> get newlySelected;
  Set<DateTime> get newlyDeselected;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalculateOvulationForAffectedCyclesImplCopyWith<
          _$CalculateOvulationForAffectedCyclesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveOvulationDatesForCyclesImplCopyWith<$Res> {
  factory _$$RemoveOvulationDatesForCyclesImplCopyWith(
          _$RemoveOvulationDatesForCyclesImpl value,
          $Res Function(_$RemoveOvulationDatesForCyclesImpl) then) =
      __$$RemoveOvulationDatesForCyclesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Set<DateTime> periodDatesToRemove});
}

/// @nodoc
class __$$RemoveOvulationDatesForCyclesImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res,
        _$RemoveOvulationDatesForCyclesImpl>
    implements _$$RemoveOvulationDatesForCyclesImplCopyWith<$Res> {
  __$$RemoveOvulationDatesForCyclesImplCopyWithImpl(
      _$RemoveOvulationDatesForCyclesImpl _value,
      $Res Function(_$RemoveOvulationDatesForCyclesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodDatesToRemove = null,
  }) {
    return _then(_$RemoveOvulationDatesForCyclesImpl(
      null == periodDatesToRemove
          ? _value._periodDatesToRemove
          : periodDatesToRemove // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _$RemoveOvulationDatesForCyclesImpl
    implements _RemoveOvulationDatesForCycles {
  const _$RemoveOvulationDatesForCyclesImpl(
      final Set<DateTime> periodDatesToRemove)
      : _periodDatesToRemove = periodDatesToRemove;

  final Set<DateTime> _periodDatesToRemove;
  @override
  Set<DateTime> get periodDatesToRemove {
    if (_periodDatesToRemove is EqualUnmodifiableSetView)
      return _periodDatesToRemove;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_periodDatesToRemove);
  }

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.removeOvulationDatesForCycles(periodDatesToRemove: $periodDatesToRemove)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveOvulationDatesForCyclesImpl &&
            const DeepCollectionEquality()
                .equals(other._periodDatesToRemove, _periodDatesToRemove));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_periodDatesToRemove));

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveOvulationDatesForCyclesImplCopyWith<
          _$RemoveOvulationDatesForCyclesImpl>
      get copyWith => __$$RemoveOvulationDatesForCyclesImplCopyWithImpl<
          _$RemoveOvulationDatesForCyclesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return removeOvulationDatesForCycles(periodDatesToRemove);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return removeOvulationDatesForCycles?.call(periodDatesToRemove);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (removeOvulationDatesForCycles != null) {
      return removeOvulationDatesForCycles(periodDatesToRemove);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return removeOvulationDatesForCycles(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return removeOvulationDatesForCycles?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (removeOvulationDatesForCycles != null) {
      return removeOvulationDatesForCycles(this);
    }
    return orElse();
  }
}

abstract class _RemoveOvulationDatesForCycles
    implements ManagePeriodTrackingEvent {
  const factory _RemoveOvulationDatesForCycles(
          final Set<DateTime> periodDatesToRemove) =
      _$RemoveOvulationDatesForCyclesImpl;

  Set<DateTime> get periodDatesToRemove;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RemoveOvulationDatesForCyclesImplCopyWith<
          _$RemoveOvulationDatesForCyclesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearSelectionImplCopyWith<$Res> {
  factory _$$ClearSelectionImplCopyWith(_$ClearSelectionImpl value,
          $Res Function(_$ClearSelectionImpl) then) =
      __$$ClearSelectionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearSelectionImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingEventCopyWithImpl<$Res, _$ClearSelectionImpl>
    implements _$$ClearSelectionImplCopyWith<$Res> {
  __$$ClearSelectionImplCopyWithImpl(
      _$ClearSelectionImpl _value, $Res Function(_$ClearSelectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearSelectionImpl implements _ClearSelection {
  const _$ClearSelectionImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.clearSelection()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearSelectionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(Set<DateTime> selectedDates) selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function(Set<DateTime> periodDates)
        calculateOvulationDates,
    required TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)
        calculateOvulationForAffectedCycles,
    required TResult Function(Set<DateTime> periodDatesToRemove)
        removeOvulationDatesForCycles,
    required TResult Function() clearSelection,
  }) {
    return clearSelection();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult? Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult? Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult? Function()? clearSelection,
  }) {
    return clearSelection?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(Set<DateTime> selectedDates)? selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function(Set<DateTime> periodDates)? calculateOvulationDates,
    TResult Function(
            Set<DateTime> newlySelected, Set<DateTime> newlyDeselected)?
        calculateOvulationForAffectedCycles,
    TResult Function(Set<DateTime> periodDatesToRemove)?
        removeOvulationDatesForCycles,
    TResult Function()? clearSelection,
    required TResult orElse(),
  }) {
    if (clearSelection != null) {
      return clearSelection();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_CalculateOvulationDates value)
        calculateOvulationDates,
    required TResult Function(_CalculateOvulationForAffectedCycles value)
        calculateOvulationForAffectedCycles,
    required TResult Function(_RemoveOvulationDatesForCycles value)
        removeOvulationDatesForCycles,
    required TResult Function(_ClearSelection value) clearSelection,
  }) {
    return clearSelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult? Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult? Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult? Function(_ClearSelection value)? clearSelection,
  }) {
    return clearSelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_CalculateOvulationDates value)? calculateOvulationDates,
    TResult Function(_CalculateOvulationForAffectedCycles value)?
        calculateOvulationForAffectedCycles,
    TResult Function(_RemoveOvulationDatesForCycles value)?
        removeOvulationDatesForCycles,
    TResult Function(_ClearSelection value)? clearSelection,
    required TResult orElse(),
  }) {
    if (clearSelection != null) {
      return clearSelection(this);
    }
    return orElse();
  }
}

abstract class _ClearSelection implements ManagePeriodTrackingEvent {
  const factory _ClearSelection() = _$ClearSelectionImpl;
}

/// @nodoc
mixin _$ManagePeriodTrackingState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManagePeriodTrackingStateCopyWith<$Res> {
  factory $ManagePeriodTrackingStateCopyWith(ManagePeriodTrackingState value,
          $Res Function(ManagePeriodTrackingState) then) =
      _$ManagePeriodTrackingStateCopyWithImpl<$Res, ManagePeriodTrackingState>;
}

/// @nodoc
class _$ManagePeriodTrackingStateCopyWithImpl<$Res,
        $Val extends ManagePeriodTrackingState>
    implements $ManagePeriodTrackingStateCopyWith<$Res> {
  _$ManagePeriodTrackingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ManagePeriodTrackingState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements ManagePeriodTrackingState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<$Res> {
  factory _$$SuccessImplCopyWith(
          _$SuccessImpl value, $Res Function(_$SuccessImpl) then) =
      __$$SuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res, _$SuccessImpl>
    implements _$$SuccessImplCopyWith<$Res> {
  __$$SuccessImplCopyWithImpl(
      _$SuccessImpl _value, $Res Function(_$SuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SuccessImpl implements _Success {
  const _$SuccessImpl();

  @override
  String toString() {
    return 'ManagePeriodTrackingState.success()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return success();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return success?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _Success implements ManagePeriodTrackingState {
  const factory _Success() = _$SuccessImpl;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<$Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl value, $Res Function(_$FailureImpl) then) =
      __$$FailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodTrackingFailure failure});

  $PeriodTrackingFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureImplCopyWithImpl<$Res>
    extends _$ManagePeriodTrackingStateCopyWithImpl<$Res, _$FailureImpl>
    implements _$$FailureImplCopyWith<$Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl _value, $Res Function(_$FailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingFailure,
    ));
  }

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodTrackingFailureCopyWith<$Res> get failure {
    return $PeriodTrackingFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureImpl implements _Failure {
  const _$FailureImpl(this.failure);

  @override
  final PeriodTrackingFailure failure;

  @override
  String toString() {
    return 'ManagePeriodTrackingState.failure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      __$$FailureImplCopyWithImpl<_$FailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return failure(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return failure?.call(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class _Failure implements ManagePeriodTrackingState {
  const factory _Failure(final PeriodTrackingFailure failure) = _$FailureImpl;

  PeriodTrackingFailure get failure;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
