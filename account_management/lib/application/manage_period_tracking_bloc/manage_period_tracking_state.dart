part of 'manage_period_tracking_bloc.dart';

@freezed
class ManagePeriodTrackingState with _$ManagePeriodTrackingState {
  const factory ManagePeriodTrackingState.initial() = _Initial;
  const factory ManagePeriodTrackingState.loading() = _Loading;
  const factory ManagePeriodTrackingState.success() = _Success;
  const factory ManagePeriodTrackingState.failure(PeriodTrackingFailure failure) = _Failure;
}
