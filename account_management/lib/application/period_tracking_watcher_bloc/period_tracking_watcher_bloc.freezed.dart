// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_tracking_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PeriodTrackingWatcherEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)
        periodDataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_PeriodDataReceived value) periodDataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_PeriodDataReceived value)? periodDataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_PeriodDataReceived value)? periodDataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodTrackingWatcherEventCopyWith<$Res> {
  factory $PeriodTrackingWatcherEventCopyWith(PeriodTrackingWatcherEvent value,
          $Res Function(PeriodTrackingWatcherEvent) then) =
      _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
          PeriodTrackingWatcherEvent>;
}

/// @nodoc
class _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
        $Val extends PeriodTrackingWatcherEvent>
    implements $PeriodTrackingWatcherEventCopyWith<$Res> {
  _$PeriodTrackingWatcherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WatchYearStartedImplCopyWith<$Res> {
  factory _$$WatchYearStartedImplCopyWith(_$WatchYearStartedImpl value,
          $Res Function(_$WatchYearStartedImpl) then) =
      __$$WatchYearStartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year});
}

/// @nodoc
class __$$WatchYearStartedImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
        _$WatchYearStartedImpl>
    implements _$$WatchYearStartedImplCopyWith<$Res> {
  __$$WatchYearStartedImplCopyWithImpl(_$WatchYearStartedImpl _value,
      $Res Function(_$WatchYearStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
  }) {
    return _then(_$WatchYearStartedImpl(
      null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$WatchYearStartedImpl implements _WatchYearStarted {
  const _$WatchYearStartedImpl(this.year);

  @override
  final int year;

  @override
  String toString() {
    return 'PeriodTrackingWatcherEvent.watchYearStarted(year: $year)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WatchYearStartedImpl &&
            (identical(other.year, year) || other.year == year));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WatchYearStartedImplCopyWith<_$WatchYearStartedImpl> get copyWith =>
      __$$WatchYearStartedImplCopyWithImpl<_$WatchYearStartedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)
        periodDataReceived,
  }) {
    return watchYearStarted(year);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
  }) {
    return watchYearStarted?.call(year);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
    required TResult orElse(),
  }) {
    if (watchYearStarted != null) {
      return watchYearStarted(year);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_PeriodDataReceived value) periodDataReceived,
  }) {
    return watchYearStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_PeriodDataReceived value)? periodDataReceived,
  }) {
    return watchYearStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_PeriodDataReceived value)? periodDataReceived,
    required TResult orElse(),
  }) {
    if (watchYearStarted != null) {
      return watchYearStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchYearStarted implements PeriodTrackingWatcherEvent {
  const factory _WatchYearStarted(final int year) = _$WatchYearStartedImpl;

  int get year;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WatchYearStartedImplCopyWith<_$WatchYearStartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PeriodDataReceivedImplCopyWith<$Res> {
  factory _$$PeriodDataReceivedImplCopyWith(_$PeriodDataReceivedImpl value,
          $Res Function(_$PeriodDataReceivedImpl) then) =
      __$$PeriodDataReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Either<PeriodTrackingFailure,
              Map<String, Map<String, PeriodTrackingModel>>>
          failureOrData});
}

/// @nodoc
class __$$PeriodDataReceivedImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
        _$PeriodDataReceivedImpl>
    implements _$$PeriodDataReceivedImplCopyWith<$Res> {
  __$$PeriodDataReceivedImplCopyWithImpl(_$PeriodDataReceivedImpl _value,
      $Res Function(_$PeriodDataReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrData = null,
  }) {
    return _then(_$PeriodDataReceivedImpl(
      null == failureOrData
          ? _value.failureOrData
          : failureOrData // ignore: cast_nullable_to_non_nullable
              as Either<PeriodTrackingFailure,
                  Map<String, Map<String, PeriodTrackingModel>>>,
    ));
  }
}

/// @nodoc

class _$PeriodDataReceivedImpl implements _PeriodDataReceived {
  const _$PeriodDataReceivedImpl(this.failureOrData);

  @override
  final Either<PeriodTrackingFailure,
      Map<String, Map<String, PeriodTrackingModel>>> failureOrData;

  @override
  String toString() {
    return 'PeriodTrackingWatcherEvent.periodDataReceived(failureOrData: $failureOrData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodDataReceivedImpl &&
            (identical(other.failureOrData, failureOrData) ||
                other.failureOrData == failureOrData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrData);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PeriodDataReceivedImplCopyWith<_$PeriodDataReceivedImpl> get copyWith =>
      __$$PeriodDataReceivedImplCopyWithImpl<_$PeriodDataReceivedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)
        periodDataReceived,
  }) {
    return periodDataReceived(failureOrData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
  }) {
    return periodDataReceived?.call(failureOrData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Either<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
    required TResult orElse(),
  }) {
    if (periodDataReceived != null) {
      return periodDataReceived(failureOrData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_PeriodDataReceived value) periodDataReceived,
  }) {
    return periodDataReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_PeriodDataReceived value)? periodDataReceived,
  }) {
    return periodDataReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_PeriodDataReceived value)? periodDataReceived,
    required TResult orElse(),
  }) {
    if (periodDataReceived != null) {
      return periodDataReceived(this);
    }
    return orElse();
  }
}

abstract class _PeriodDataReceived implements PeriodTrackingWatcherEvent {
  const factory _PeriodDataReceived(
      final Either<PeriodTrackingFailure,
              Map<String, Map<String, PeriodTrackingModel>>>
          failureOrData) = _$PeriodDataReceivedImpl;

  Either<PeriodTrackingFailure, Map<String, Map<String, PeriodTrackingModel>>>
      get failureOrData;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PeriodDataReceivedImplCopyWith<_$PeriodDataReceivedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PeriodTrackingWatcherState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)
        loadSuccess,
    required TResult Function(PeriodTrackingFailure failure) loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult? Function(PeriodTrackingFailure failure)? loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult Function(PeriodTrackingFailure failure)? loadFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodTrackingWatcherStateCopyWith<$Res> {
  factory $PeriodTrackingWatcherStateCopyWith(PeriodTrackingWatcherState value,
          $Res Function(PeriodTrackingWatcherState) then) =
      _$PeriodTrackingWatcherStateCopyWithImpl<$Res,
          PeriodTrackingWatcherState>;
}

/// @nodoc
class _$PeriodTrackingWatcherStateCopyWithImpl<$Res,
        $Val extends PeriodTrackingWatcherState>
    implements $PeriodTrackingWatcherStateCopyWith<$Res> {
  _$PeriodTrackingWatcherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)
        loadSuccess,
    required TResult Function(PeriodTrackingFailure failure) loadFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult? Function(PeriodTrackingFailure failure)? loadFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult Function(PeriodTrackingFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements PeriodTrackingWatcherState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)
        loadSuccess,
    required TResult Function(PeriodTrackingFailure failure) loadFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult? Function(PeriodTrackingFailure failure)? loadFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult Function(PeriodTrackingFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements PeriodTrackingWatcherState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadSuccessImplCopyWith<$Res> {
  factory _$$LoadSuccessImplCopyWith(
          _$LoadSuccessImpl value, $Res Function(_$LoadSuccessImpl) then) =
      __$$LoadSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Map<String, Map<String, PeriodTrackingModel>> yearData,
      Map<String, Set<DateTime>>? futurePredictions});
}

/// @nodoc
class __$$LoadSuccessImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherStateCopyWithImpl<$Res, _$LoadSuccessImpl>
    implements _$$LoadSuccessImplCopyWith<$Res> {
  __$$LoadSuccessImplCopyWithImpl(
      _$LoadSuccessImpl _value, $Res Function(_$LoadSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? yearData = null,
    Object? futurePredictions = freezed,
  }) {
    return _then(_$LoadSuccessImpl(
      null == yearData
          ? _value._yearData
          : yearData // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, PeriodTrackingModel>>,
      futurePredictions: freezed == futurePredictions
          ? _value._futurePredictions
          : futurePredictions // ignore: cast_nullable_to_non_nullable
              as Map<String, Set<DateTime>>?,
    ));
  }
}

/// @nodoc

class _$LoadSuccessImpl implements _LoadSuccess {
  const _$LoadSuccessImpl(
      final Map<String, Map<String, PeriodTrackingModel>> yearData,
      {final Map<String, Set<DateTime>>? futurePredictions})
      : _yearData = yearData,
        _futurePredictions = futurePredictions;

  final Map<String, Map<String, PeriodTrackingModel>> _yearData;
  @override
  Map<String, Map<String, PeriodTrackingModel>> get yearData {
    if (_yearData is EqualUnmodifiableMapView) return _yearData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_yearData);
  }

  final Map<String, Set<DateTime>>? _futurePredictions;
  @override
  Map<String, Set<DateTime>>? get futurePredictions {
    final value = _futurePredictions;
    if (value == null) return null;
    if (_futurePredictions is EqualUnmodifiableMapView)
      return _futurePredictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.loadSuccess(yearData: $yearData, futurePredictions: $futurePredictions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadSuccessImpl &&
            const DeepCollectionEquality().equals(other._yearData, _yearData) &&
            const DeepCollectionEquality()
                .equals(other._futurePredictions, _futurePredictions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_yearData),
      const DeepCollectionEquality().hash(_futurePredictions));

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadSuccessImplCopyWith<_$LoadSuccessImpl> get copyWith =>
      __$$LoadSuccessImplCopyWithImpl<_$LoadSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)
        loadSuccess,
    required TResult Function(PeriodTrackingFailure failure) loadFailure,
  }) {
    return loadSuccess(yearData, futurePredictions);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult? Function(PeriodTrackingFailure failure)? loadFailure,
  }) {
    return loadSuccess?.call(yearData, futurePredictions);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult Function(PeriodTrackingFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadSuccess != null) {
      return loadSuccess(yearData, futurePredictions);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return loadSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return loadSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadSuccess != null) {
      return loadSuccess(this);
    }
    return orElse();
  }
}

abstract class _LoadSuccess implements PeriodTrackingWatcherState {
  const factory _LoadSuccess(
          final Map<String, Map<String, PeriodTrackingModel>> yearData,
          {final Map<String, Set<DateTime>>? futurePredictions}) =
      _$LoadSuccessImpl;

  Map<String, Map<String, PeriodTrackingModel>> get yearData;
  Map<String, Set<DateTime>>? get futurePredictions;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadSuccessImplCopyWith<_$LoadSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadFailureImplCopyWith<$Res> {
  factory _$$LoadFailureImplCopyWith(
          _$LoadFailureImpl value, $Res Function(_$LoadFailureImpl) then) =
      __$$LoadFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodTrackingFailure failure});

  $PeriodTrackingFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$LoadFailureImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherStateCopyWithImpl<$Res, _$LoadFailureImpl>
    implements _$$LoadFailureImplCopyWith<$Res> {
  __$$LoadFailureImplCopyWithImpl(
      _$LoadFailureImpl _value, $Res Function(_$LoadFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$LoadFailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingFailure,
    ));
  }

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodTrackingFailureCopyWith<$Res> get failure {
    return $PeriodTrackingFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$LoadFailureImpl implements _LoadFailure {
  const _$LoadFailureImpl(this.failure);

  @override
  final PeriodTrackingFailure failure;

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.loadFailure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadFailureImplCopyWith<_$LoadFailureImpl> get copyWith =>
      __$$LoadFailureImplCopyWithImpl<_$LoadFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)
        loadSuccess,
    required TResult Function(PeriodTrackingFailure failure) loadFailure,
  }) {
    return loadFailure(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult? Function(PeriodTrackingFailure failure)? loadFailure,
  }) {
    return loadFailure?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult Function(PeriodTrackingFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadFailure != null) {
      return loadFailure(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    return loadFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    return loadFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    if (loadFailure != null) {
      return loadFailure(this);
    }
    return orElse();
  }
}

abstract class _LoadFailure implements PeriodTrackingWatcherState {
  const factory _LoadFailure(final PeriodTrackingFailure failure) =
      _$LoadFailureImpl;

  PeriodTrackingFailure get failure;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadFailureImplCopyWith<_$LoadFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
