import 'dart:async';
import 'package:analytics/analytics.dart';
import 'package:analytics/domain/models/device_settings_model.dart';
import 'package:analytics/domain/models/device_snapshot_model.dart';
import 'package:analytics/domain/models/therapy_feedback_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:uuid/uuid.dart';
import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:analytics/infrastructure/services/therapy_feedback_trigger_service.dart';

@LazySingleton(as: IAnalyticsFacade)
class AnalyticsFacadeImpl implements IAnalyticsFacade {
  final ILocalTelemetryDataSource _localDataSource;
  final ICloudTelemetryDataSource _cloudDataSource;
  final SessionStateManager _sessionStateManager;
  final TherapyFeedbackTriggerService _feedbackTriggerService;
  final _uuid = const Uuid();

  AnalyticsFacadeImpl(
    this._localDataSource,
    this._cloudDataSource,
    this._sessionStateManager,
    this._feedbackTriggerService,
  );

  // Enhanced therapy session management methods
  @override
  Future<Either<String, String>> startTherapySession() async {
    try {
      // Initialize session manager if not already initialized
      await _sessionStateManager.initialize();
      // Check if session already exists or is being created (prevent race conditions)
      if (_sessionStateManager.sessionId != null) {
        print(
            '🔒 Analytics: Session already active: ${_sessionStateManager.sessionId}');
        return Right(_sessionStateManager.sessionId ?? 'unknown');
      }
      // Get device info from Bluetooth facade

      String sessionId = await _sessionStateManager.startSession();

      print('📊 Analytics: Session started with ID: $sessionId');

      // Initialize device monitoring
      print('✅ Therapy session started with ID: $sessionId');

      return Right(sessionId);
    } catch (e) {
      print('❌ Error starting therapy session: $e');
      return Left('Error starting therapy session: $e');
    }
  }

  @override
  Future<Either<String, Unit>> endTherapySession() async {
    try {
      // Stop device monitoring services
      try {
        print(
            '📊 Device monitoring stopped for session: ${_sessionStateManager.sessionId}');
      } catch (e) {
        print('⚠️ Failed to stop device monitoring: $e');
      }

      await _sessionStateManager.endSession();

      print('✅ Therapy session ended successfully');

      return Right(unit);
    } catch (e) {
      print('❌ Error ending therapy session: $e');
      return Left('Error ending therapy session: $e');
    }
  }

  Future<Either<String, Unit>> logSessionEvent({
    required String sessionId,
    required String eventType,
    required Map<String, dynamic> eventData,
    bool isDeviceInitiated = false,
    String? source,
  }) async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      final sessionIndex =
          sessions.indexWhere((s) => s.sessionInfo.sessionId == sessionId);

      if (sessionIndex == -1) {
        return Left('Session not found');
      }

      final session = sessions[sessionIndex];
      final event = TherapySessionEventModel(
        eventId: _uuid.v4(),
        sessionId: sessionId,
        eventType: eventType,
        timestamp: DateTime.now(),
        eventData: eventData,
        isDeviceInitiated: isDeviceInitiated,
      );

      final updatedEvents =
          List<TherapySessionEventModel>.from(session.sessionEvents)
            ..add(event);

      final updatedSession = session.copyWith(
        sessionEvents: updatedEvents,
      );
      await _localDataSource.saveTherapySession(updatedSession);
      print('✅ logSessionEvent: Event logged successfully');
      return Right(unit);
    } catch (e) {
      return Left('Error logging session event: $e');
    }
  }

  @override
  Future<Either<String, Unit>> logSettingChange({
    required TherapySessionEventModel event,
  }) async {
    try {
      // Ensure a session is active - start one if needed
      final sessionResult = await startTherapySession();

      sessionResult.mapBoth(
        onLeft: (error) {
          print('❌ Failed to start/get session: $error');
          return;
        },
        onRight: (sessionId) {
          print('✅ Session active for setting change: $sessionId');
        },
      );

      // Update the event with the correct active session ID
      final updatedEvent =
          event.copyWith(sessionId: _sessionStateManager.sessionId);

      // Save the event locally (for individual event tracking)
      await _localDataSource.saveEvent(updatedEvent);

      // Add the event to the current active session's events list
      final sessions = await _localDataSource.getTherapySessions();
      print('📊 Found ${sessions.length} sessions in local storage');
      print('🔍 Looking for session: ${_sessionStateManager.sessionId}');

      final sessionIndex = sessions.indexWhere(
          (s) => s.sessionInfo.sessionId == _sessionStateManager.sessionId);

      if (sessionIndex != -1) {
        final session = sessions[sessionIndex];
        print(
            '✅ Found session with ${session.sessionEvents.length} existing events');

        final updatedSession = session.copyWith(
          sessionEvents: [...session.sessionEvents, updatedEvent],
          // If this is a setting change event, update most used settings
          mostUsedSettings: updatedEvent.eventType == 'setting_change'
              ? _calculateMostUsedSettings(session, updatedEvent)
              : session.mostUsedSettings,
        );

        await _localDataSource.saveTherapySession(updatedSession);
        print(
            '✅ Added event to session: ${updatedEvent.eventType} -> ${_sessionStateManager.sessionId} (now ${updatedSession.sessionEvents.length} events)');
        print('✅ logSettingChange: Setting change logged successfully');
      } else {
        print(
            '⚠️ Could not find active session to add event: ${_sessionStateManager.sessionId}');
        print(
            '📋 Available sessions: ${sessions.map((s) => s.sessionInfo.sessionId).toList()}');

        // Try to get the session directly from SessionStateManager
        final currentSession = _sessionStateManager.currentSession;
        if (currentSession != null) {
          print(
              '🔄 Found session in SessionStateManager, adding event there');
          final updatedSession = currentSession.copyWith(
            sessionEvents: [...currentSession.sessionEvents, updatedEvent],
            mostUsedSettings: updatedEvent.eventType == 'setting_change'
                ? _calculateMostUsedSettings(currentSession, updatedEvent)
                : currentSession.mostUsedSettings,
          );
          await _localDataSource.saveTherapySession(updatedSession);
          print(
              '✅ Added event via SessionStateManager: ${updatedEvent.eventType}');
        }
      }

      return Right(unit);
    } catch (e) {
      print('❌ Error logging setting change: $e');
      return Left('Error logging setting change: $e');
    }
  }

// Helper method to calculate most used settings
  DeviceSettingsModel _calculateMostUsedSettings(
      TherapySessionModel session, TherapySessionEventModel newEvent) {
    // TODO: Implement a more sophisticated algorithm to calculate most used settings
    // For now, just use the new event's settings if it has heat and tens settings
    if (newEvent.eventType == 'setting_change') {
      final int heatLevel = newEvent.eventData.containsKey('newHeatLevel')
          ? newEvent.eventData['newHeatLevel'] as int
          : session.mostUsedSettings.heatLevel;

      final int tensLevel = newEvent.eventData.containsKey('newTensLevel')
          ? newEvent.eventData['newTensLevel'] as int
          : session.mostUsedSettings.tensLevel;

      final int tensMode = newEvent.eventData.containsKey('newTensMode')
          ? newEvent.eventData['newTensMode'] as int
          : session.mostUsedSettings.tensMode;

      return DeviceSettingsModel(
        heatLevel: heatLevel,
        tensLevel: tensLevel,
        tensMode: tensMode,
        batteryLevel: session.mostUsedSettings.batteryLevel,
        timestamp: DateTime.now(),
      );
    }

    // Otherwise keep current most used settings
    return session.mostUsedSettings;
  }

  @override
  Future<Either<String, List<TherapySessionModel>>> getStoredSessions() async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      return Right(sessions);
    } catch (e) {
      return Left('Error getting stored sessions: $e');
    }
  }

  @override
  Future<Either<String, List<TherapySessionModel>>>
      getUnsyncedSessions() async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      final unsyncedSessions =
          sessions.where((s) => !s.isSyncedToCloud).toList();
      return Right(unsyncedSessions);
    } catch (e) {
      return Left('Error getting unsynced sessions: $e');
    }
  }

  @override
  Future<Either<String, TherapySessionModel?>> getCurrentSession() async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      final currentSession =
          sessions.where((s) => s.status == 'active').firstOrNull;
      return Right(currentSession);
    } catch (e) {
      return Left('Error getting current session: $e');
    }
  }

  @override
  Future<Either<String, Unit>> syncSessionsToCloud() async {
    try {
      print('🔄 Starting sync of sessions to cloud');

      // Get all unsynced sessions
      final unsynced = await _localDataSource.getUnsyncedSessions();
      if (unsynced.isEmpty) {
        print('✅ No sessions to sync');
        return Right(unit);
      }

      print('🔄 Found ${unsynced.length} unsynced sessions to sync');

      int successCount = 0;
      List<String> errors = [];

      // Try to sync each session
      for (final session in unsynced) {
        try {
          final success = await _cloudDataSource.saveTherapySession(session);
          if (success) {
            // Mark as synced locally
            await _localDataSource
                .markSessionSynced(session.sessionInfo.sessionId);
            successCount++;
            print(
                '✅ Successfully synced session: ${session.sessionInfo.sessionId}');
          } else {
            errors
                .add('Failed to sync session ${session.sessionInfo.sessionId}');
            print(
                '❌ Failed to sync session: ${session.sessionInfo.sessionId}');
          }
        } catch (e) {
          errors.add(
              'Error syncing session ${session.sessionInfo.sessionId}: $e');
          print(
              '❌ Error syncing session ${session.sessionInfo.sessionId}: $e');
        }
      }

      if (errors.isNotEmpty) {
        return Left(
            'Synced $successCount/${unsynced.length} sessions. Errors: ${errors.join(", ")}');
      }

      print('✅ Successfully synced all $successCount sessions');
      return Right(unit);
    } catch (e) {
      print('❌ Error syncing sessions to cloud: $e');
      return Left('Error syncing sessions to cloud: $e');
    }
  }

  /// Force re-sync all sessions (for debugging)
  @override
  Future<Either<String, Unit>> forceResyncAllSessions() async {
    try {
      // TEMPORARY: Clear all old session data to fix serialization issues
      print(
          '🗑️ CLEARING ALL OLD SESSION DATA (temporary fix for serialization)');
      await _clearAllSessionData();
      print('✅ All old session data cleared');

      final sessions = await _localDataSource.getTherapySessions();
      print('🔄 Force re-syncing ${sessions.length} sessions...');

      for (final session in sessions) {
        print(
            '🔄 Force syncing session: ${session.sessionInfo.sessionId} (currently synced: ${session.isSyncedToCloud})');

        // Reset sync status to false first
        final unsyncedSession = session.copyWith(isSyncedToCloud: false);
        await _localDataSource.saveTherapySession(unsyncedSession);

        // Now try to sync
        final cloudSuccess =
            await _cloudDataSource.saveTherapySession(unsyncedSession);
        if (cloudSuccess) {
          await _localDataSource
              .markSessionSynced(session.sessionInfo.sessionId);
          print(
              '✅ Force synced session: ${session.sessionInfo.sessionId}');
        } else {
          print(
              '❌ Force sync failed for session: ${session.sessionInfo.sessionId}');
        }
      }
      return Right(unit);
    } catch (e) {
      return Left('Error force syncing sessions: $e');
    }
  }

  /// Clear all session data (temporary method to fix serialization issues)
  Future<void> _clearAllSessionData() async {
    try {
      // Clear therapy sessions
      await _localDataSource.clearAllTherapySessions();

      // Clear telemetry events
      await _localDataSource.clearAllEvents();

      print('🗑️ All session data cleared from local storage');
    } catch (e) {
      print('❌ Error clearing session data: $e');
    }
  }

  // Additional session management methods
  Future<Either<String, Unit>> pauseSession({required String sessionId}) async {
    try {
      await logSessionEvent(
        sessionId: sessionId,
        eventType: 'session_paused',
        eventData: {'timestamp': DateTime.now().toIso8601String()},
        isDeviceInitiated: false,
        source: 'app',
      );
      return Right(unit);
    } catch (e) {
      return Left('Error pausing session: $e');
    }
  }

  Future<Either<String, Unit>> resumeSession(
      {required String sessionId}) async {
    try {
      await logSessionEvent(
        sessionId: sessionId,
        eventType: 'session_resumed',
        eventData: {'timestamp': DateTime.now().toIso8601String()},
        isDeviceInitiated: false,
        source: 'app',
      );
      return Right(unit);
    } catch (e) {
      return Left('Error resuming session: $e');
    }
  }

  // @override
  // Future<Either<String, Map<String, dynamic>>> getSessionAnalytics(
  //     {required String sessionId}) async {
  //   try {
  //     final sessions = await _localDataSource.getTherapySessions();
  //     final session =
  //         sessions.where((s) => s.sessionInfo.sessionId == sessionId).firstOrNull;
  //
  //     if (session == null) {
  //       return Left('Session not found');
  //     }
  //
  //     final analytics = {
  //       'sessionId': sessionId,
  //       'duration': session.durationSeconds,
  //       'eventCount': session.sessionEvents.length,
  //       'snapshotCount': session.deviceSnapshots.length,
  //       'mostUsedSettings': session.mostUsedSettings,
  //       'initialSettings': session.initialSettings,
  //       'finalSettings': session.finalSettings,
  //       'status': session.status,
  //     };
  //
  //     return Right(analytics);
  //   } catch (e) {
  //     return Left('Error getting session analytics: $e');
  //   }
  // }

  @override
  Future<Either<String, Map<String, dynamic>>> getMostUsedSettings() async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      if (sessions.isEmpty) {
        return Right({
          'message': 'No sessions found',
          'settings': {},
        });
      }

      // TODO: Implement algorithm to find most commonly used settings
      // This is a simple implementation that just counts occurrences of each setting
      final heatLevelCounts = <int, int>{};
      final tensLevelCounts = <int, int>{};
      final tensModeCounts = <int, int>{};

      // Count settings across all sessions
      for (final session in sessions) {
        // Count initial settings
        _incrementCount(heatLevelCounts, session.initialSettings.heatLevel);
        _incrementCount(tensLevelCounts, session.initialSettings.tensLevel);
        _incrementCount(tensModeCounts, session.initialSettings.tensMode);

        // Count final settings
        _incrementCount(heatLevelCounts, session.finalSettings.heatLevel);
        _incrementCount(tensLevelCounts, session.finalSettings.tensLevel);
        _incrementCount(tensModeCounts, session.finalSettings.tensMode);

        // Count settings from events
        for (final event in session.sessionEvents) {
          if (event.eventType == 'setting_change') {
            if (event.eventData.containsKey('newHeatLevel')) {
              final heatLevel = event.eventData['newHeatLevel'] as int?;
              if (heatLevel != null) {
                _incrementCount(heatLevelCounts, heatLevel);
              }
            }

            if (event.eventData.containsKey('newTensLevel')) {
              final tensLevel = event.eventData['newTensLevel'] as int?;
              if (tensLevel != null) {
                _incrementCount(tensLevelCounts, tensLevel);
              }
            }

            if (event.eventData.containsKey('newTensMode')) {
              final tensMode = event.eventData['newTensMode'] as int?;
              if (tensMode != null) {
                _incrementCount(tensModeCounts, tensMode);
              }
            }
          }
        }
      }

      // Find most used settings
      final mostUsedHeatLevel = _findMostFrequent(heatLevelCounts);
      final mostUsedTensLevel = _findMostFrequent(tensLevelCounts);
      final mostUsedTensMode = _findMostFrequent(tensModeCounts);

      return Right({
        'heatLevel': mostUsedHeatLevel,
        'tensLevel': mostUsedTensLevel,
        'tensMode': mostUsedTensMode,
        'sessionsAnalyzed': sessions.length,
      });
    } catch (e) {
      print('❌ Error getting most used settings: $e');
      return Left('Error getting most used settings: $e');
    }
  }

  // Helper method to increment count in a map
  void _incrementCount(Map<int, int> counts, int value) {
    counts[value] = (counts[value] ?? 0) + 1;
  }

  // Helper method to find the most frequent value in a map
  int _findMostFrequent(Map<int, int> counts) {
    if (counts.isEmpty) return 0;

    int mostFrequentValue = counts.keys.first;
    int highestCount = counts.values.first;

    counts.forEach((value, count) {
      if (count > highestCount) {
        mostFrequentValue = value;
        highestCount = count;
      }
    });

    return mostFrequentValue;
  }

  @override
  Future<Either<String, Unit>> saveDeviceSnapshot({
    required String sessionId,
    required Map<String, dynamic> snapshot,
    DateTime? timestamp,
  }) async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      final sessionIndex =
          sessions.indexWhere((s) => s.sessionInfo.sessionId == sessionId);

      if (sessionIndex == -1) {
        return Left('Session not found');
      }

      final session = sessions[sessionIndex];
      DeviceSnapshotModel deviceSnapshot;

      try {
        deviceSnapshot = DeviceSnapshotModel.fromJson(snapshot);
      } catch (e) {
        return Left('Invalid snapshot format: $e');
      }

      final updatedSession = session.copyWith(
        deviceSnapshot: [...session.deviceSnapshot, deviceSnapshot],
        lastSnapshotTime: timestamp ?? DateTime.now(),
      );

      final success = await _localDataSource.saveTherapySession(updatedSession);

      if (success) {
        return Right(unit);
      } else {
        return Left('Failed to save device snapshot');
      }
    } catch (e) {
      print('❌ Error saving device snapshot: $e');
      return Left('Error saving device snapshot: $e');
    }
  }

  @override
  Future<Either<String, List<TherapySessionModel>>>
      getTherapySessionsLocal() async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      return Right(sessions);
    } catch (e) {
      print('❌ Error getting local therapy sessions: $e');
      return Left('Error getting local therapy sessions: $e');
    }
  }

  @override
  Future<Either<String, List<TherapySessionModel>>>
      getTherapySessionsFromCloud() async {
    try {
      final sessions = await _cloudDataSource.getTherapySessions();
      return Right(sessions);
    } catch (e) {
      print('❌ Error getting therapy sessions from cloud: $e');
      return Left('Error getting therapy sessions from cloud: $e');
    }
  }

  @override
  Future<Either<String, List<TherapySessionModel>>>
      getTherapySessionsWithFallback() async {
    try {
      // First try to get from local storage
      final localSessions = await _localDataSource.getTherapySessions();

      if (localSessions.isNotEmpty) {
        print('✅ Found ${localSessions.length} sessions in local storage');
        return Right(localSessions);
      }

      // If local storage is empty, try cloud storage
      print('📡 Local storage empty, trying cloud storage...');
      final cloudSessions = await _cloudDataSource.getTherapySessions();

      if (cloudSessions.isNotEmpty) {
        print('✅ Found ${cloudSessions.length} sessions in cloud storage');

        // Save cloud sessions to local storage for future use
        for (final session in cloudSessions) {
          await _localDataSource.saveTherapySession(session);
        }
        print('💾 Saved cloud sessions to local storage');

        return Right(cloudSessions);
      }

      print('📭 No sessions found in local or cloud storage');
      return Right([]);
    } catch (e) {
      print('❌ Error getting therapy sessions with fallback: $e');
      return Left('Error getting therapy sessions with fallback: $e');
    }
  }

  @override
  Future<Either<String, Unit>> syncTherapySessionsToCloud() async {
    // This is essentially the same as syncSessionsToCloud
    return syncSessionsToCloud();
  }

  @override
  Future<String?> getCurrentSessionId() async {
    return _sessionStateManager.sessionId;
  }

  @override
  Future<Either<String, Unit>> saveDeviceLog({
    required String sessionId,
    required Map<String, dynamic> log,
    DateTime? timestamp,
  }) async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      final sessionIndex =
          sessions.indexWhere((s) => s.sessionInfo.sessionId == sessionId);

      if (sessionIndex == -1) {
        return Left('Session not found');
      }

      final session = sessions[sessionIndex];

      final updatedSession = session.copyWith(
        deviceLogs: [...session.deviceLogs, log],
      );

      final success = await _localDataSource.saveTherapySession(updatedSession);

      if (success) {
        return Right(unit);
      } else {
        return Left('Failed to save device log');
      }
    } catch (e) {
      print('❌ Error saving device log: $e');
      return Left('Error saving device log: $e');
    }
  }

  /// Initialize dependencies after DI setup (to avoid circular dependency)
  Future<void> initializeDependencies() async {
    // Get the DeviceSnapshotService from DI and set it on SessionStateManager
    try {
      print('✅ Analytics dependencies initialized');
    } catch (e) {
      print('❌ Error initializing analytics dependencies: $e');
    }
  }

  // === 🎯 Feedback operations ===
  @override
  Future<Either<String, Unit>> submitSessionFeedback({
    required String sessionId,
    String? feedbackText,
    int? painLevelBefore,
    int? painLevelAfter,
  }) async {
    try {
      // Get the session
      final sessions = await _localDataSource.getTherapySessions();
      final sessionIndex = sessions.indexWhere(
        (s) => s.sessionInfo.sessionId == sessionId,
      );

      if (sessionIndex == -1) {
        return Left('Session not found');
      }

      final session = sessions[sessionIndex];

      // Create feedback model
      final feedback = TherapyFeedbackModel(
        feedbackText: feedbackText,
        painLevelBefore: painLevelBefore,
        painLevelAfter: painLevelAfter,
        feedbackRequested: true,
        feedbackCompleted: true,
        feedbackSubmittedAt: DateTime.now(),
      );

      // Update session with feedback
      final updatedSession = session.copyWith(
        feedback: feedback,
      );

      // Save locally
      await _localDataSource.saveTherapySession(updatedSession);

      // Sync to cloud
      await _cloudDataSource.saveTherapySession(updatedSession);

      // Cancel any pending feedback notification since feedback is now completed
      try {
        final notificationsFacade =
            GetIt.instance<ScheduledNotificationsFacade>();
        await notificationsFacade
            .cancelSingleNotification('feedback_$sessionId');
        print(
            '✅ Canceled feedback notification for completed session: $sessionId');
      } catch (e) {
        print('⚠️ Could not cancel notification: $e');
      }

      print('✅ Session feedback submitted for session: $sessionId');
      return Right(unit);
    } catch (e) {
      print('❌ Error submitting session feedback: $e');
      return Left('Error submitting feedback: $e');
    }
  }

  @override
  Future<Either<String, Unit>> scheduleSessionFeedbackNotification({
    required String sessionId,
    required Duration delay,
  }) async {
    try {
      final notificationsFacade =
          GetIt.instance<ScheduledNotificationsFacade>();

      final scheduledTime = DateTime.now().add(delay);
      final tz.TZDateTime tzScheduledTime =
          tz.TZDateTime.from(scheduledTime, tz.local);

      await notificationsFacade.scheduleSingleNotification(
        body: 'How was your therapy session? Please share your feedback.',
        dateTime: tzScheduledTime,
        notificationId: 'feedback_$sessionId',
        notificationType: 'therapy_feedback',
        title: 'Therapy Feedback',
        payload: sessionId,
        isForeground: false,
      );

      print('✅ Feedback notification scheduled for session: $sessionId');
      return Right(unit);
    } catch (e) {
      print('❌ Error scheduling feedback notification: $e');
      return Left('Error scheduling notification: $e');
    }
  }

  @override
  Future<Either<String, Unit>> markFeedbackRequested(String sessionId) async {
    try {
      final sessions = await _localDataSource.getTherapySessions();
      final sessionIndex = sessions.indexWhere(
        (s) => s.sessionInfo.sessionId == sessionId,
      );

      if (sessionIndex == -1) {
        return Left('Session not found');
      }

      final session = sessions[sessionIndex];
      final feedback = session.feedback?.copyWith(feedbackRequested: true) ??
          TherapyFeedbackModel.empty().copyWith(feedbackRequested: true);
      final updatedSession = session.copyWith(feedback: feedback);

      await _localDataSource.saveTherapySession(updatedSession);

      print('✅ Feedback marked as requested for session: $sessionId');
      return Right(unit);
    } catch (e) {
      print('❌ Error marking feedback as requested: $e');
      return Left('Error marking feedback as requested: $e');
    }
  }

  // === 🎯 Feedback trigger operations ===
  @override
  Stream<String> get feedbackTriggerStream =>
      _feedbackTriggerService.feedbackTriggerStream;

  @override
  Future<void> checkNotificationPayload(String? payload) async {
    await _feedbackTriggerService.checkNotificationPayload(payload);
  }

  @override
  Future<void> checkMostRecentSession() async {
    await _feedbackTriggerService.checkMostRecentSession();
  }
}
