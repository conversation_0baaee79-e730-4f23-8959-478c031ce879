import 'package:authentication/domain/facade/i_auth_facade.dart';
import 'package:authentication/repository/auth_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:injectable/injectable.dart';

import '../../domain/models/therapy_session_model.dart';

abstract class ICloudTelemetryDataSource {
  Future<bool> saveTherapySession(TherapySessionModel session);
  Future<List<TherapySessionModel>> getTherapySessions();
}

@LazySingleton(as: ICloudTelemetryDataSource)
class FirebaseTelemetryDataSource implements ICloudTelemetryDataSource {
  final FirebaseFirestore _firestore;
  final IAuthFacade _authFacade;

  FirebaseTelemetryDataSource(this._firestore, this._authFacade);

  @override
  Future<bool> saveTherapySession(TherapySessionModel session) async {
    try {
      // ignore: avoid_print
     debugPrint(
          '🔄 Attempting to save therapy session to Firestore: ${session.sessionInfo.sessionId}');

      final userOption = await _authFacade.getSignedInUser();
      final user = userOption.getOrNull();
      if (user == null) {
        return false;
      }
      final updatedSession = session.copyWith(
        sessionInfo: session.sessionInfo.copyWith(userId: user.uid),
      );
      final firestoreData = updatedSession.toJson();
      firestoreData.addAll({'syncedAt': FieldValue.serverTimestamp()});
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('therapy_sessions')
          .doc(updatedSession.sessionInfo.sessionId)
          .set(firestoreData);
      return true;
    } catch (e) {
      // ignore: avoid_print
     debugPrint('❌ Failed to save therapy session: $e');
      return false;
    }
  }

  @override
  Future<List<TherapySessionModel>> getTherapySessions() async {
    try {
      // ignore: avoid_print
     debugPrint('🔄 Attempting to retrieve therapy sessions from Firestore');

      final userOption = await _authFacade.getSignedInUser();
      final user = userOption.getOrNull();
      if (user == null) {
        // ignore: avoid_print
       debugPrint('❌ No authenticated user found');
        return [];
      }

      final querySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('therapy_sessions')
          .orderBy('sessionInfo.therapyStartTime', descending: true)
          .limit(5) // Get latest 5 sessions
          .get();

      final sessions = <TherapySessionModel>[];
      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          final session = TherapySessionModel.fromJson(data);
          sessions.add(session);
        } catch (e) {
          // ignore: avoid_print
         debugPrint('❌ Error parsing session ${doc.id}: $e');
          // Continue with other sessions even if one fails to parse
        }
      }

      // ignore: avoid_print
     debugPrint(
          '✅ Successfully retrieved ${sessions.length} therapy sessions from Firestore');
      return sessions;
    } catch (e) {
      // ignore: avoid_print
     debugPrint('❌ Failed to retrieve therapy sessions from Firestore: $e');
      return [];
    }
  }
}
