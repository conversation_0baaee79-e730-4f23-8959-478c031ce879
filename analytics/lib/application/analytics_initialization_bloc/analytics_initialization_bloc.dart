import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../../infrastructure/services/analytics_initialization_service.dart';

part 'analytics_initialization_event.dart';
part 'analytics_initialization_state.dart';

@injectable
class AnalyticsInitializationBloc extends Bloc<AnalyticsInitializationEvent, AnalyticsInitializationState> {
  final AnalyticsInitializationService _analyticsInitService;

  AnalyticsInitializationBloc(this._analyticsInitService) : super(AnalyticsInitializationInitial()) {
    on<InitializeAnalyticsEvent>(_onInitializeAnalytics);
    on<CheckInitializationStatusEvent>(_onCheckInitializationStatus);
  }

  Future<void> _onInitializeAnalytics(
    InitializeAnalyticsEvent event,
    Emitter<AnalyticsInitializationState> emit,
  ) async {
    emit(AnalyticsInitializationLoading());

    try {
      print('🚀 Starting Analytics Initialization...');
      
      // Initialize analytics services and check for unsynced sessions
      await _analyticsInitService.initializeOnAppStart();
      
      // Get initialization stats
      final stats = await _analyticsInitService.getInitializationStats();
      
      emit(AnalyticsInitializationSuccess(
        message: 'Analytics services initialized successfully',
        stats: stats,
      ));
      
      print('✅ Analytics Initialization Completed Successfully');
      
    } catch (error) {
      print('❌ Analytics Initialization Failed: $error');
      
      emit(AnalyticsInitializationFailure(
        error: error.toString(),
      ));
    }
  }

  Future<void> _onCheckInitializationStatus(
    CheckInitializationStatusEvent event,
    Emitter<AnalyticsInitializationState> emit,
  ) async {
    try {
      final stats = await _analyticsInitService.getInitializationStats();
      final isInitialized = _analyticsInitService.isInitialized;
      
      if (isInitialized) {
        emit(AnalyticsInitializationSuccess(
          message: 'Analytics services are initialized',
          stats: stats,
        ));
      } else {
        emit(AnalyticsInitializationInitial());
      }
    } catch (error) {
      emit(AnalyticsInitializationFailure(error: error.toString()));
    }
  }
}
