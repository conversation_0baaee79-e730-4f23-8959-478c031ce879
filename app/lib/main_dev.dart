import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get_it/get_it.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'app.dart';
import 'di.dart';
import 'helpers.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SharedPreferences and register it with GetIt
  final prefs = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(prefs);

  // Initialize Firebase first
  await Firebase.initializeApp();
  tz.initializeTimeZones();

  // Register core Firebase services
  getIt.registerSingleton<FirebaseFirestore>(FirebaseFirestore.instance);
  // getIt.registerSingleton<FirebaseAuth>(FirebaseAuth.instance);
  getIt.registerSingleton<FirebaseMessaging>(FirebaseMessaging.instance);

  // Activate app check after Firebase registration
  await FirebaseAppCheck.instance.activate(
      appleProvider: AppleProvider.appAttest,
      androidProvider: AndroidProvider.playIntegrity);
  // Configure all package dependencies in the correct order
  await configureAllPackagesDependencies(envId: 'Dev');

 debugPrint('Dependencies configured successfully');


  runApp(JunoPlus(env: 'Dev'));
}


// Function to handle feedback dialog


