import 'package:account_management/domain/model/period_tracking_model.dart';

class CycleDayCalculator {
  /// Calculates which day of the menstrual cycle a given date represents
  /// Returns null if the date is not part of any identifiable cycle
  static CycleDayInfo? calculateCycleDay(
    DateTime targetDate,
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) {
    print('🔍 CycleDayCalculator: Calculating for date: $targetDate');

    // Get all period dates from the year data
    final periodDates = <DateTime>[];
    final allOvulationDatesInData = <DateTime>[];

    for (final monthData in yearData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isPeriodDate == true && dayData.date != null) {
          periodDates.add(dayData.date!);
        }
        if (dayData.isOvulationDate == true && dayData.date != null) {
          allOvulationDatesInData.add(dayData.date!);
        }
      }
    }

    print(
        '🔍 CycleDayCalculator: Found ${periodDates.length} period dates: $periodDates');
    print(
        '🔍 CycleDayCalculator: Found ${allOvulationDatesInData.length} ovulation dates in database: $allOvulationDatesInData');

    if (periodDates.isEmpty) {
      print('🔍 CycleDayCalculator: No period dates found, returning null');
      return null;
    }

    // Sort period dates
    periodDates.sort();

    // Find the cycle that contains the target date
    DateTime? cycleStart;
    DateTime? nextCycleStart;

    for (int i = 0; i < periodDates.length; i++) {
      final periodDate = periodDates[i];

      // Check if this is the start of a new cycle (first period date or gap > 10 days from previous)
      bool isNewCycle = i == 0;
      if (i > 0) {
        final daysSincePrevious =
            periodDate.difference(periodDates[i - 1]).inDays;
        if (daysSincePrevious > 10) {
          isNewCycle = true;
        }
      }

      print(
          '🔍 CycleDayCalculator: Period date $periodDate, isNewCycle: $isNewCycle');

      if (isNewCycle) {
        // This is a cycle start
        if (targetDate.isAfter(periodDate.subtract(Duration(days: 1))) ||
            targetDate.isAtSameMomentAs(periodDate)) {
          cycleStart = periodDate;
          print(
              '🔍 CycleDayCalculator: Found potential cycle start: $cycleStart');

          // Find the next cycle start - look for the next period that starts a new cycle
          nextCycleStart = null; // Reset to null first
          for (int j = i + 1; j < periodDates.length; j++) {
            final nextPeriodDate = periodDates[j];
            final daysSinceCurrent =
                nextPeriodDate.difference(periodDate).inDays;
            print(
                '🔍 CycleDayCalculator: Checking next period date $nextPeriodDate, days since current: $daysSinceCurrent');

            // Simplified: just look for any period date that's more than 10 days after current cycle start
            if (daysSinceCurrent > 10) {
              nextCycleStart = nextPeriodDate;
              print(
                  '🔍 CycleDayCalculator: Found next cycle start: $nextCycleStart');
              break;
            }
          }

          print(
              '🔍 CycleDayCalculator: Final next cycle start: $nextCycleStart');

          // If target date is before the next cycle start (or no next cycle), this is our cycle
          if (nextCycleStart == null || targetDate.isBefore(nextCycleStart)) {
            print('🔍 CycleDayCalculator: This is our cycle!');
            break;
          }
        }
      }
    }

    if (cycleStart == null) {
      print('🔍 CycleDayCalculator: No cycle start found, returning null');
      return null;
    }

    print(
        '🔍 CycleDayCalculator: Cycle start: $cycleStart, Next cycle start: $nextCycleStart');

    // Calculate the cycle day
    final daysDifference = targetDate.difference(cycleStart).inDays + 1;

    // Determine cycle length (default to 28 if no next cycle)
    int cycleLength = 28;
    if (nextCycleStart != null) {
      final calculatedLength = nextCycleStart.difference(cycleStart).inDays;
      // Ensure cycle length is reasonable (between 21 and 45 days)
      if (calculatedLength >= 21 && calculatedLength <= 45) {
        cycleLength = calculatedLength;
      }
    }

    // Ensure cycle day is valid (between 1 and cycle length)
    final validCycleDay = daysDifference.clamp(1, cycleLength);

    print(
        '🔍 CycleDayCalculator: Days difference: $daysDifference, Cycle length: $cycleLength, Valid cycle day: $validCycleDay');

    // Determine if it's a period day
    bool isPeriodDay = false;
    for (final periodDate in periodDates) {
      if (_isSameDay(targetDate, periodDate)) {
        isPeriodDay = true;
        break;
      }
    }

    // Determine if it's an ovulation day (only check actual database data)
    bool isOvulationDay = false;
    for (final monthData in yearData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isOvulationDate == true &&
            dayData.date != null &&
            _isSameDay(targetDate, dayData.date!)) {
          isOvulationDay = true;
          break;
        }
      }
      if (isOvulationDay) break;
    }

    // Calculate actual period length for this cycle
    final actualPeriodLength = _calculatePeriodLength(cycleStart, periodDates);

    // Extract ovulation dates for this cycle
    final cycleOvulationDates = <DateTime>[];
    final allOvulationDates = <DateTime>[];

    for (final monthData in yearData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isOvulationDate == true && dayData.date != null) {
          final ovulationDate = dayData.date!;
          allOvulationDates.add(ovulationDate);

          print('🔍 CycleDayCalculator: Found ovulation date: $ovulationDate');
          print(
              '🔍 CycleDayCalculator: Cycle start: $cycleStart, Next cycle start: $nextCycleStart');
          print(
              '🔍 CycleDayCalculator: Is after cycle start-1? ${ovulationDate.isAfter(cycleStart.subtract(Duration(days: 1)))}');
          print(
              '🔍 CycleDayCalculator: Is before next cycle start? ${nextCycleStart == null ? 'null (no next cycle)' : ovulationDate.isBefore(nextCycleStart)}');

          // Include ovulation dates that fall within this cycle
          if (ovulationDate.isAfter(cycleStart.subtract(Duration(days: 1))) &&
              (nextCycleStart == null ||
                  ovulationDate.isBefore(nextCycleStart))) {
            cycleOvulationDates.add(ovulationDate);
            print(
                '🔍 CycleDayCalculator: ✅ Added ovulation date to cycle: $ovulationDate');
          } else {
            print(
                '🔍 CycleDayCalculator: ❌ Excluded ovulation date from cycle: $ovulationDate');
          }
        }
      }
    }

    print(
        '🔍 CycleDayCalculator: All ovulation dates found: $allOvulationDates');

    print(
        '🔍 CycleDayCalculator: Found ${cycleOvulationDates.length} ovulation dates for this cycle: $cycleOvulationDates');
    print(
        '🔍 CycleDayCalculator: Is period day: $isPeriodDay, Is ovulation day: $isOvulationDay');
    print('🔍 CycleDayCalculator: Actual period length: $actualPeriodLength');

    final result = CycleDayInfo(
      cycleDay: validCycleDay,
      cycleLength: cycleLength,
      isPeriodDay: isPeriodDay,
      isOvulationDay: isOvulationDay,
      cycleStartDate: cycleStart,
      periodLength: actualPeriodLength,
      ovulationDates: cycleOvulationDates,
    );

    print(
        '🔍 CycleDayCalculator: Final result - Cycle phase: ${result.cyclePhase}');
    return result;
  }

  /// Calculates the actual period length for a given cycle start
  static int _calculatePeriodLength(
      DateTime cycleStart, List<DateTime> periodDates) {
    int periodLength = 0;

    // Count consecutive period days starting from cycle start
    for (int i = 0; i < 10; i++) {
      // Max 10 days to avoid infinite loops
      final checkDate = cycleStart.add(Duration(days: i));
      bool foundPeriodDay = false;

      for (final periodDate in periodDates) {
        if (_isSameDay(checkDate, periodDate)) {
          foundPeriodDay = true;
          break;
        }
      }

      if (foundPeriodDay) {
        periodLength++;
      } else {
        break; // Stop counting when we hit a non-period day
      }
    }

    return periodLength > 0
        ? periodLength
        : 5; // Default to 5 if no consecutive days found
  }

  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}

class CycleDayInfo {
  final int cycleDay;
  final int cycleLength;
  final bool isPeriodDay;
  final bool isOvulationDay;
  final DateTime cycleStartDate;
  final int periodLength;
  final List<DateTime> ovulationDates;

  const CycleDayInfo({
    required this.cycleDay,
    required this.cycleLength,
    required this.isPeriodDay,
    required this.isOvulationDay,
    required this.cycleStartDate,
    required this.periodLength,
    required this.ovulationDates,
  });

  String get cyclePhase {
    if (isPeriodDay) return 'Menstrual';
    if (cycleDay <= 5) return 'Menstrual';
    if (isOvulationDay) return 'Ovulation';
    if (cycleDay <= 13) return 'Follicular';
    return 'Luteal';
  }
}
