import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';

class SymptomIconUploader {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Upload symptom icons from assets to Firebase Storage and update Firestore
  static Future<void> uploadSymptomIcons(BuildContext context) async {
    try {
      print('🔄 Starting symptom icon upload process...');
      
      // Show loading dialog
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('Uploading symptom icons...'),
            ],
          ),
        ),
      );

      // Define category mappings (folder name -> Firestore category name)
      final categoryMappings = {
        'Digestion': 'Digestion',
        'Flow': 'Flow',
        'Mood': 'Mood',
        'Pain': 'Pain',
        'Skin': 'Skin',
        'Sleep': 'Sleep',
      };

      // Get existing symptoms from Firestore
      final symptomsSnapshot = await _firestore.collection('symptoms_master').get();
      final existingSymptoms = <String, Map<String, dynamic>>{};
      
      for (final doc in symptomsSnapshot.docs) {
        final data = doc.data();
        final symptoms = data['symptoms'] as List<dynamic>? ?? [];
        existingSymptoms[doc.id] = {
          'symptoms': symptoms,
          'docRef': doc.reference,
        };
      }

      print('📊 Found ${existingSymptoms.length} existing symptom categories');

      // Process each category folder
      for (final entry in categoryMappings.entries) {
        final folderName = entry.key;
        final categoryName = entry.value;
        
        print('📁 Processing category: $categoryName (folder: $folderName)');
        
        // Get icon files from assets folder
        final manifestContent = await rootBundle.loadString('AssetManifest.json');
        final Map<String, dynamic> manifestMap = 
            json.decode(manifestContent) as Map<String, dynamic>;
        
        final iconFiles = manifestMap.keys
            .where((String key) => key.startsWith('assets/icons/$folderName/'))
            .where((String key) => key.endsWith('.svg'))
            .toList();

        print('🎨 Found ${iconFiles.length} icons in $folderName folder');

        final List<Map<String, dynamic>> categorySymptoms = [];
        
        // Process each icon file
        for (final iconPath in iconFiles) {
          final fileName = iconPath.split('/').last.replaceAll('.svg', '');
          final symptomName = _formatSymptomName(fileName);
          
          print('📤 Uploading icon: $fileName -> $symptomName');
          
          try {
            // Load asset data
            final ByteData data = await rootBundle.load(iconPath);
            final Uint8List bytes = data.buffer.asUint8List();
            
            // Upload to Firebase Storage
            final storageRef = _storage.ref().child('symptom_icons/$categoryName/$fileName.svg');
            final uploadTask = await storageRef.putData(
              bytes,
              SettableMetadata(contentType: 'image/svg+xml'),
            );
            
            // Get download URL
            final downloadUrl = await uploadTask.ref.getDownloadURL();
            
            // Create symptom object
            categorySymptoms.add({
              'name': symptomName,
              'category': categoryName,
              'iconUrl': downloadUrl,
              'colorCode': _getCategoryColor(categoryName),
            });
            
            print('✅ Successfully uploaded: $symptomName');
            
          } catch (e) {
            print('❌ Failed to upload $fileName: $e');
          }
        }
        
        // Update or create Firestore document for this category
        if (categorySymptoms.isNotEmpty) {
          final docRef = _firestore.collection('symptoms_master').doc(categoryName);
          await docRef.set({
            'symptoms': categorySymptoms,
            'lastUpdated': FieldValue.serverTimestamp(),
          });
          
          print('💾 Updated Firestore document for $categoryName with ${categorySymptoms.length} symptoms');
        }
      }
      
      // Close loading dialog
      Navigator.of(context).pop();
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Symptom icons uploaded successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      
      print('🎉 Symptom icon upload process completed successfully!');
      
    } catch (e) {
      // Close loading dialog if open
      Navigator.of(context).pop();
      
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Error uploading icons: $e'),
          backgroundColor: Colors.red,
        ),
      );
      
      print('💥 Error in symptom icon upload: $e');
    }
  }

  /// Format file name to proper symptom name
  static String _formatSymptomName(String fileName) {
    return fileName
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : word)
        .join(' ');
  }

  /// Get category color code
  static String _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'mood':
        return '#FF6B6B';
      case 'pain':
        return '#FF8E53';
      case 'flow':
        return '#FF69B4';
      case 'skin':
        return '#4ECDC4';
      case 'sleep':
        return '#45B7D1';
      case 'digestion':
        return '#96CEB4';
      default:
        return '#584294';
    }
  }
}
