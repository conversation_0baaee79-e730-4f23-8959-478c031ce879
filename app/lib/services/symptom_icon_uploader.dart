import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';

class SymptomIconUploader {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Debug function to print existing symptoms from Firestore and available assets
  static Future<void> debugSymptomsAndAssets(BuildContext context) async {
    try {
      print('🔍 === DEBUGGING SYMPTOMS AND ASSETS ===');

      // Show loading dialog
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('Analyzing symptoms and assets...'),
            ],
          ),
        ),
      );

      // 1. Get existing symptoms from Firestore
      print('\n📊 === EXISTING SYMPTOMS IN FIRESTORE ===');
      final symptomsSnapshot =
          await _firestore.collection('symptoms_master').get();

      if (symptomsSnapshot.docs.isEmpty) {
        print('❌ No symptoms found in symptoms_master collection');
      } else {
        for (final doc in symptomsSnapshot.docs) {
          final data = doc.data();
          final categoryName = doc.id;
          final symptoms = data['symptoms'] as List<dynamic>? ?? [];

          print('\n📁 Category: $categoryName');
          print('   Document ID: ${doc.id}');
          print('   Total symptoms: ${symptoms.length}');

          if (symptoms.isNotEmpty) {
            print('   Symptoms:');
            for (int i = 0; i < symptoms.length; i++) {
              final symptom = symptoms[i] as Map<String, dynamic>;
              final name = symptom['name'] ?? 'Unknown';
              final iconUrl = symptom['iconUrl'] ?? 'No URL';
              final hasIcon =
                  iconUrl != 'No URL' && iconUrl.toString().isNotEmpty;
              print(
                  '     ${i + 1}. $name ${hasIcon ? '✅' : '❌'} ${hasIcon ? '' : '(No icon)'}');
            }
          }
        }
      }

      // 2. Get available assets from folders
      print('\n🎨 === AVAILABLE ASSETS IN FOLDERS ===');
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap =
          json.decode(manifestContent) as Map<String, dynamic>;

      // Get all icon files
      final allIconFiles = manifestMap.keys
          .where((String key) => key.startsWith('assets/icons/'))
          .where((String key) => key.endsWith('.svg'))
          .toList();

      print('Total icon files found: ${allIconFiles.length}');

      // Group by folder
      final Map<String, List<String>> iconsByFolder = {};
      for (final iconPath in allIconFiles) {
        final pathParts = iconPath.split('/');
        if (pathParts.length >= 3) {
          final folderName = pathParts[2]; // assets/icons/[FolderName]/file.svg
          final fileName = pathParts.last.replaceAll('.svg', '');

          if (!iconsByFolder.containsKey(folderName)) {
            iconsByFolder[folderName] = [];
          }
          iconsByFolder[folderName]!.add(fileName);
        }
      }

      for (final entry in iconsByFolder.entries) {
        final folderName = entry.key;
        final fileNames = entry.value;

        print('\n📁 Folder: $folderName');
        print('   Total icons: ${fileNames.length}');
        print('   Icons:');
        for (int i = 0; i < fileNames.length; i++) {
          final formattedName = _formatSymptomName(fileNames[i]);
          print('     ${i + 1}. ${fileNames[i]} -> "$formattedName"');
        }
      }

      // 3. Compare and suggest matches
      print('\n🔄 === MATCHING ANALYSIS ===');
      for (final doc in symptomsSnapshot.docs) {
        final categoryName = doc.id;
        final data = doc.data();
        final symptoms = data['symptoms'] as List<dynamic>? ?? [];

        print('\n📊 Category: $categoryName');

        // Find matching folder
        String? matchingFolder;
        for (final folderName in iconsByFolder.keys) {
          if (folderName.toLowerCase() == categoryName.toLowerCase()) {
            matchingFolder = folderName;
            break;
          }
        }

        if (matchingFolder != null) {
          print('   ✅ Found matching folder: $matchingFolder');
          final availableIcons = iconsByFolder[matchingFolder]!;

          print('   Symptom matching:');
          for (final symptomData in symptoms) {
            final symptom = symptomData as Map<String, dynamic>;
            final symptomName = symptom['name'] ?? 'Unknown';

            // Try to find matching icon with improved matching logic
            String? matchingIcon;
            for (final iconFileName in availableIcons) {
              final formattedIconName = _formatSymptomName(iconFileName);

              // Direct match
              if (formattedIconName.toLowerCase() ==
                  symptomName.toLowerCase()) {
                matchingIcon = iconFileName;
                break;
              }

              // Handle common variations
              if (_isSymptomMatch(symptomName, formattedIconName)) {
                matchingIcon = iconFileName;
                break;
              }
            }

            if (matchingIcon != null) {
              print('     ✅ $symptomName -> $matchingIcon');
            } else {
              print('     ❌ $symptomName -> No matching icon found');
              print('         Available icons: ${availableIcons.join(', ')}');
            }
          }
        } else {
          print('   ❌ No matching folder found for category: $categoryName');
          print('       Available folders: ${iconsByFolder.keys.join(', ')}');
        }
      }

      // Close loading dialog
      Navigator.of(context).pop();

      // Show completion message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('🔍 Debug analysis completed! Check console for details.'),
          backgroundColor: Colors.blue,
        ),
      );

      print('\n🎉 === DEBUG ANALYSIS COMPLETED ===');
    } catch (e) {
      // Close loading dialog if open
      Navigator.of(context).pop();

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Error during debug analysis: $e'),
          backgroundColor: Colors.red,
        ),
      );

      print('💥 Error in debug analysis: $e');
    }
  }

  /// Upload symptom icons from assets to Firebase Storage and update Firestore
  static Future<void> uploadSymptomIcons(BuildContext context) async {
    try {
      print('🔄 Starting symptom icon upload process...');

      // Show loading dialog
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('Uploading symptom icons...'),
            ],
          ),
        ),
      );

      // Define category mappings (folder name -> Firestore category name)
      final categoryMappings = {
        'Digestion': 'digestive',
        'Flow': 'flow',
        'Mood': 'mood',
        'Pain': 'cramps_&_pain',
        'Skin': 'skin',
        'Sleep': 'sleep',
      };

      // Get existing symptoms from Firestore
      final symptomsSnapshot =
          await _firestore.collection('symptoms_master').get();
      final existingSymptoms = <String, Map<String, dynamic>>{};

      for (final doc in symptomsSnapshot.docs) {
        final data = doc.data();
        final symptoms = data['symptoms'] as List<dynamic>? ?? [];
        existingSymptoms[doc.id] = {
          'symptoms': symptoms,
          'docRef': doc.reference,
        };
      }

      print('📊 Found ${existingSymptoms.length} existing symptom categories');

      // Process each category folder
      for (final entry in categoryMappings.entries) {
        final folderName = entry.key;
        final categoryName = entry.value;

        print('📁 Processing category: $categoryName (folder: $folderName)');

        // Get icon files from assets folder
        final manifestContent =
            await rootBundle.loadString('AssetManifest.json');
        final Map<String, dynamic> manifestMap =
            json.decode(manifestContent) as Map<String, dynamic>;

        final iconFiles = manifestMap.keys
            .where((String key) => key.startsWith('assets/icons/$folderName/'))
            .where((String key) => key.endsWith('.svg'))
            .toList();

        print('🎨 Found ${iconFiles.length} icons in $folderName folder');

        final List<Map<String, dynamic>> categorySymptoms = [];

        // Process each icon file
        for (final iconPath in iconFiles) {
          final fileName = iconPath.split('/').last.replaceAll('.svg', '');
          final symptomName = _formatSymptomName(fileName);

          print('📤 Uploading icon: $fileName -> $symptomName');

          try {
            // Load asset data
            final ByteData data = await rootBundle.load(iconPath);
            final Uint8List bytes = data.buffer.asUint8List();

            // Upload to Firebase Storage
            final storageRef = _storage
                .ref()
                .child('symptom_icons/$categoryName/$fileName.svg');
            final uploadTask = await storageRef.putData(
              bytes,
              SettableMetadata(contentType: 'image/svg+xml'),
            );

            // Get download URL
            final downloadUrl = await uploadTask.ref.getDownloadURL();

            // Create symptom object
            categorySymptoms.add({
              'name': symptomName,
              'category': categoryName,
              'iconUrl': downloadUrl,
              'colorCode': _getCategoryColor(categoryName),
            });

            print('✅ Successfully uploaded: $symptomName');
          } catch (e) {
            print('❌ Failed to upload $fileName: $e');
          }
        }

        // Update or create Firestore document for this category
        if (categorySymptoms.isNotEmpty) {
          final docRef =
              _firestore.collection('symptoms_master').doc(categoryName);
          await docRef.set({
            'symptoms': categorySymptoms,
            'lastUpdated': FieldValue.serverTimestamp(),
          });

          print(
              '💾 Updated Firestore document for $categoryName with ${categorySymptoms.length} symptoms');
        }
      }

      // Close loading dialog
      Navigator.of(context).pop();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Symptom icons uploaded successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      print('🎉 Symptom icon upload process completed successfully!');
    } catch (e) {
      // Close loading dialog if open
      Navigator.of(context).pop();

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Error uploading icons: $e'),
          backgroundColor: Colors.red,
        ),
      );

      print('💥 Error in symptom icon upload: $e');
    }
  }

  /// Format file name to proper symptom name
  static String _formatSymptomName(String fileName) {
    return fileName
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : word)
        .join(' ');
  }

  /// Check if symptom name matches icon name with common variations
  static bool _isSymptomMatch(String symptomName, String iconName) {
    final symptomLower = symptomName.toLowerCase();
    final iconLower = iconName.toLowerCase();

    // Define common symptom name variations
    final variations = {
      // Mood variations
      'anxiety': 'anxious',
      'depression': 'depressed',
      'irritability': 'angry',

      // Sleep variations
      'restless sleep': 'restless',
      'fatigue': 'well_rested', // This might need manual review

      // Skin variations
      'body breakouts': 'body_acne',

      // Digestive variations
      'bloating': 'bloated',
      'nausea': 'vomiting', // Close match, might need manual review

      // Pain variations
      'headaches': 'migraine',
      'cramps': 'ovulation_pain', // Close match, might need manual review
      'joint pain': 'leg_pain', // Close match, might need manual review
    };

    // Check direct variations
    if (variations[symptomLower] == iconLower.replaceAll(' ', '_')) {
      return true;
    }

    // Check reverse variations
    for (final entry in variations.entries) {
      if (entry.value == iconLower.replaceAll(' ', '_') &&
          entry.key == symptomLower) {
        return true;
      }
    }

    // Check if symptom name contains icon name or vice versa
    if (symptomLower.contains(iconLower.replaceAll('_', ' ')) ||
        iconLower.replaceAll('_', ' ').contains(symptomLower)) {
      return true;
    }

    return false;
  }

  /// Get category color code
  static String _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'mood':
        return '#FF6B6B';
      case 'pain':
      case 'cramps_&_pain':
        return '#FF8E53';
      case 'flow':
        return '#FF69B4';
      case 'skin':
        return '#4ECDC4';
      case 'sleep':
        return '#45B7D1';
      case 'digestion':
      case 'digestive':
        return '#96CEB4';
      default:
        return '#584294';
    }
  }
}
