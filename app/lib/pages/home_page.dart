import 'package:account_management/account_management.dart';
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart';
import 'package:analytics/analytics.dart' hide getIt;
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/bluetooth.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';

import 'package:juno_plus/pages/dashboard/period_tracking_view_page.dart';
import 'package:juno_plus/pages/settings/settings_page.dart';
import 'package:notifications/application/custom_notification_stream/custom_notification_stream_bloc.dart';
import 'package:notifications/application/notification_bloc/notification_bloc.dart';
import 'package:remote/application/device_control_bloc/device_control_bloc.dart';
import '../helpers.dart';
import '../routing/app_pages.gr.dart';

import 'dashboard/new_dashboard_page.dart';
import 'therapy/therapy_feedback_bottom_sheet.dart';
import 'connectivity/connectivity_mapper.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final List<String> _tabs = [
    'Calendar',
    'Remote',
    'Tracking',
    'Dashboard',
    'Settings'
  ];
  String _selectedTab = 'Dashboard';
  // late AppUpdateCheck _appUpdateCheck;
  // Create an instance of your controller
  // final TensController _controller = TensController();

  /// Show therapy feedback bottom sheet for the most recent session
  void _showTherapyFeedback(BuildContext context) async {
    try {
      // Get the most recent therapy session from analytics
      final analyticsFacade = getIt<IAnalyticsFacade>();
      final sessionsResult = await analyticsFacade.getStoredSessions();

      sessionsResult.mapBoth(
        onLeft: (String error) {
          debugPrint('❌ Error getting therapy sessions: $error');
        },
        onRight: (List<TherapySessionModel> sessions) {
          if (sessions.isNotEmpty) {
            // Get the most recent session (first in list)
            final latestSession = sessions.first;
            debugPrint(
                '🎯 Showing feedback for session: ${latestSession.sessionInfo.sessionId}');

            // Show the feedback bottom sheet
            TherapyFeedbackBottomSheet.show(
                context, latestSession.sessionInfo.sessionId);
          } else {
            debugPrint('⚠️ No therapy sessions found for feedback');
          }
        },
      );
    } catch (e) {
      debugPrint('❌ Error showing therapy feedback: $e');
    }
  }

  @override
  void dispose() {
    // Make sure to dispose the controller when the screen is disposed
    // _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // _initializeAppUpdateCheck();
    // _checkForNotifications();

    // Start background scanning for saved devices when app comes to home page
    _initializeBackgroundScanning();
  }

  /// Initialize background scanning for saved devices
  void _initializeBackgroundScanning() {
    // Get the bluetooth service bloc from the app-level provider and start background scanning
    final bluetoothBloc = context.read<BluetoothServiceBloc>();
    // This will start the background scanning process for saved devices
    bluetoothBloc.add(const BluetoothServiceEvent.checkBluetooth());
  }

  // Future<void> _initializeAppUpdateCheck() async {
  //   FirebaseRemoteConfig remoteConfig = await FirebaseRemoteConfig.instance;
  //   _appUpdateCheck = AppUpdateCheck(remoteConfig);
  //   // Check for updates
  //   _appUpdateCheck.checkForUpdate(context);
  // }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => getIt<NotificationBloc>()
                ..add(NotificationEvent.initialize(context)),
            ),
            BlocProvider(
              create: (_) => getIt<CustomNotificationStreamBloc>()
                ..add(CustomNotificationStreamEvent
                    .loadCustomNotificationStream()),
            ),
            BlocProvider(
              create: (_) => getIt<DeviceControlBloc>(),
            ),
          ],
          child: MultiBlocListener(
            listeners: [
              BlocListener<NotificationBloc, NotificationState>(
                listener: (context, state) {
                  state.maybeMap(
                    success: (_) {},
                    failure: (failure) {},
                    orElse: () {},
                  );
                },
              ),

              BlocListener<MenstrualCycleBloc, MenstrualCycleState>(
                listener: (context, state) {},
              ),
              // Analytics initialization listener
              BlocListener<AnalyticsInitializationBloc,
                  AnalyticsInitializationState>(
                listener: (context, state) {
                  if (state is AnalyticsInitializationLoading) {
                    // Show loading indicator for analytics initialization
                    if (kDebugMode) {
                      debugPrint('🔄 Analytics services initializing...');
                    }
                  } else if (state is AnalyticsInitializationSuccess) {
                    // Analytics initialized successfully
                    if (kDebugMode) {
                      debugPrint('✅ Analytics initialized successfully');
                      debugPrint('📊 Stats: ${state.stats}');

                      // Show detailed stats
                      final stats =
                          state.stats['storageStats'] as Map<String, dynamic>?;
                      if (stats != null) {
                        debugPrint(
                            '📱 Total sessions: ${stats['totalSessions']}');
                        debugPrint(
                            '📤 Unsynced sessions: ${stats['unsyncedSessions']}');
                        debugPrint('🌐 Online: ${stats['isOnline']}');
                        debugPrint('🔄 Syncing: ${stats['isSyncing']}');
                      }
                    }

                    // Check if most recent session needs feedback
                    getIt<IAnalyticsFacade>().checkMostRecentSession();

                    // Optional: Show success snackbar in debug mode
                    if (kDebugMode) {
                      // ScaffoldMessenger.of(context).showSnackBar(
                      // SnackBar(
                      //   content: Text('Analytics services ready'),
                      //   backgroundColor: Colors.green,
                      //   duration: Duration(seconds: 2),
                      // ),
                      // );
                    }
                  } else if (state is AnalyticsInitializationFailure) {
                    // Analytics initialization failed
                    if (kDebugMode) {
                      debugPrint(
                          '❌ Analytics initialization failed: ${state.error}');
                    }

                    // Show error toast in debug mode
                    if (kDebugMode) {
                      Fluttertoast.showToast(
                        msg: 'Analytics initialization failed',
                        toastLength: Toast.LENGTH_LONG,
                        gravity: ToastGravity.BOTTOM,
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                      );
                    }
                  }
                },
              ),
              // Therapy feedback listener
              BlocListener<DeviceControlBloc, DeviceControlState>(
                listener: (context, state) {
                  state.whenOrNull(
                    therapyStateChanged: (isActive) {
                      // When therapy ends (was active, now inactive)
                      if (!isActive) {
                        debugPrint(
                            '🎯 Therapy ended, showing feedback bottom sheet');
                        _showTherapyFeedback(context);
                      }
                    },
                  );
                },
              ),
              // Therapy management listener
              // BlocListener<TherapyManagementBloc, TherapyManagementState>(
              //   listener: (context, state) {
              //     state.maybeMap(
              //       loading: (_) {
              //         if (kDebugMode) {
              //          debugPrint('🔄 Starting therapy session...');
              //         }
              //       },
              //       therapyStarted: (_) {
              //         if (kDebugMode) {
              //          debugPrint('✅ Therapy session started successfully!');
              //          debugPrint(
              //               '📊 Session will run for 5 minutes and then auto-end');
              //         }
              //
              //         // Show success snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content:
              //                 Text('Therapy session started! (5 min duration)'),
              //             backgroundColor: Colors.green,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       error: (errorState) {
              //         if (kDebugMode) {
              //          debugPrint(
              //               '❌ Failed to start therapy session: ${errorState.message}');
              //         }
              //
              //         // Show error snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content: Text('Failed to start therapy session'),
              //             backgroundColor: Colors.red,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       orElse: () {},
              //     );
              //   },
              // ),
              // listen for custom notification stream
              BlocListener<CustomNotificationStreamBloc,
                  CustomNotificationStreamState>(
                listener: (context, state) {
                  state.maybeMap(
                    successNotification: (notification) {
                      if (notification.notification != null) {
                        print(
                            'Notification received: ${notification.notification}');
                        final notificationModel = notification.notification!;
                        print(
                            'opening notification: ${notificationModel.title}');

                        // Handle therapy feedback notifications specifically
                        print(
                            '🔍 Processing notification: type=${notificationModel.notificationType}, payload=${notificationModel.payload}');

                        // Check for therapy feedback notification first
                        if (notificationModel.notificationType ==
                            'therapy_feedback') {
                          print(
                              '✅ Therapy feedback notification detected, calling analytics');
                          getIt<IAnalyticsFacade>().checkNotificationPayload(
                              notificationModel.payload);
                          return;
                        }

                        // Show generic notification dialog for other types
                        showDialog<void>(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: Text(
                                  notificationModel.title ?? 'Notification'),
                              content:
                                  Text(notificationModel.body ?? 'No body'),
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text('OK'),
                                ),
                              ],
                            );
                          },
                        );
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
              BlocListener<PeriodReminderSettingsBloc,
                  PeriodReminderSettingsState>(
                listener: (context, state) {
                  state.maybeMap(
                    loading: (_) {
                      if (kDebugMode) {
                        print('🔄 Loading period reminder settings...');
                      }
                    },
                    loaded: (loadedState) {
                      if (kDebugMode) {
                        print('✅ Period reminder settings loaded');
                      }
                    },
                    failure: (failureState) {
                      if (kDebugMode) {
                        print(
                            '❌ Failed to load period reminder settings: ${failureState.message}');
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
            ],
            child: StreamBuilder<String>(
              stream: getIt<IAnalyticsFacade>().feedbackTriggerStream,
              builder: (context, snapshot) {
                // When a session ID is emitted, check if feedback is still needed before showing bottom sheet
                if (snapshot.hasData && snapshot.data != null) {
                  WidgetsBinding.instance.addPostFrameCallback((_) async {
                    final sessionId = snapshot.data!;
                    print(
                        '🎯 Feedback trigger received for session: $sessionId');

                    // Double-check if session still needs feedback before showing bottom sheet
                    try {
                      final analyticsFacade = getIt<IAnalyticsFacade>();
                      final sessionsResult =
                          await analyticsFacade.getStoredSessions();

                      sessionsResult.mapBoth(
                        onLeft: (error) {
                          print('❌ Error getting sessions: $error');
                          // Fallback: show bottom sheet anyway
                          TherapyFeedbackBottomSheet.show(context, sessionId);
                        },
                        onRight: (sessions) {
                          final session = sessions
                              .where(
                                  (s) => s.sessionInfo.sessionId == sessionId)
                              .firstOrNull;

                          if (session != null &&
                              session.status == 'completed' &&
                              session.feedback?.feedbackCompleted != true) {
                            print(
                                '✅ Session still needs feedback, showing bottom sheet');
                            TherapyFeedbackBottomSheet.show(context, sessionId);
                          } else {
                            print(
                                '🚫 Session no longer needs feedback, skipping bottom sheet');
                          }
                        },
                      );
                    } catch (e) {
                      print('❌ Error checking session feedback status: $e');
                      // Fallback: show bottom sheet anyway
                      TherapyFeedbackBottomSheet.show(context, sessionId);
                    }
                  });
                }

                return Scaffold(
                    extendBody: true,
                    backgroundColor: Colors.transparent,
                    bottomNavigationBar: Padding(
                      padding: const EdgeInsets.only(
                          left: 20.0, right: 20.0, bottom: 20.0, top: 20.0),
                      child: Container(
                        width: 1.0.sw,
                        height: .18.sw,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(40)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            GestureDetector(
                                key: const Key('calendar'),
                                onTap: () {
                                  context.router
                                      .push(PeriodTrackingViewRoute());
                                },
                                child: Icon(
                                  Icons.calendar_today_rounded,
                                  size: 28,
                                  color: const Color(0xffCFB4FE),
                                )),
                            GestureDetector(
                              key: const Key('remote'),
                              onTap: () {
                                setState(() {
                                  _selectedTab = _tabs[1];
                                });

                                // setState(() {
                                //   context.router.push(RemoteOneRoute());
                                // });
                              },
                              child: SvgPicture.asset(
                                'assets/home/<USER>',
                                height: 30,
                                width: 30,
                                colorFilter: ColorFilter.mode(
                                  _selectedTab == 'Remote'
                                      ? Colors.white
                                      : const Color(0xffCFB4FE),
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedTab = _tabs[3];
                                });
                              },
                              child: SvgPicture.asset(
                                'assets/home/<USER>',
                                height: 25,
                                width: 25,
                                colorFilter: ColorFilter.mode(
                                  _selectedTab == 'Dashboard'
                                      ? Colors.white
                                      : const Color(0xffCFB4FE),
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                            GestureDetector(
                              key: const Key('settings'),
                              onTap: () {
                                setState(() {
                                  _selectedTab = _tabs[4];
                                });
                                print('Settings clicked');

                                // DEBUG: Force resync all sessions to test sync issue
                                // if (kDebugMode) {
                                //   // _debugForceResyncSessions(context);
                                // }
                              },
                              child: SvgPicture.asset(
                                'assets/home/<USER>',
                                height: 28,
                                width: 28,
                                colorFilter: ColorFilter.mode(
                                  _selectedTab == 'Settings'
                                      ? Colors.white
                                      : const Color(0xffCFB4FE),
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    body: _selectedTab == 'Dashboard'
                        ? NewDashboardPage()
                        : _selectedTab == 'Remote'
                            ? ConnectivityMapper()
                            : _selectedTab == 'Settings'
                                ? SettingsPage()
                                : PeriodTrackingViewPage() // Default to calendar for Tracking tab

                    );
              },
            ),
          ),
        ));
  }
}
