import 'dart:async';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:showcaseview/showcaseview.dart';
import 'virtual_battery_indicator_widget.dart';
import 'virtual_tens_mode.dart';
import 'virtual_tens_widget.dart';
import 'virtual_heat_widget.dart';
import 'virtual_sync_button.dart';

//global variable to be deleted
bool _showCase = false;

@RoutePage()
class VirtualRemotePage extends StatefulWidget {
  @override
  State<VirtualRemotePage> createState() => _VirtualRemotePageState();
}

class _VirtualRemotePageState extends State<VirtualRemotePage> {
  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      enableShowcase: false,
      builder: (context) {
        return VirtualRemoteScaffold();
      },
    );
  }
}

class VirtualRemoteScaffold extends StatefulWidget {
  const VirtualRemoteScaffold({super.key});

  @override
  State<VirtualRemoteScaffold> createState() => _VirtualRemoteScaffoldState();
}

class _VirtualRemoteScaffoldState extends State<VirtualRemoteScaffold> {
  final GlobalKey _tensExplainerKey = GlobalKey();
  final GlobalKey _heatExplainerKey = GlobalKey();
  final GlobalKey _syncButtonKey = GlobalKey();
  final GlobalKey _helpButtonKey = GlobalKey();
  final GlobalKey _settingsButtonKey = GlobalKey();
  final GlobalKey _modesExplainerKey = GlobalKey();
  final GlobalKey _playPauseExplainerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // _checkBinding();
  }

  Future<void> _checkBinding() async {
    if (!_showCase) {
      Future.delayed(Duration(milliseconds: 500), () {
        ShowCaseWidget.of(context).startShowCase([
          _tensExplainerKey,
          _modesExplainerKey,
          _heatExplainerKey,
          _syncButtonKey,
          _playPauseExplainerKey,
          _settingsButtonKey,
          _helpButtonKey
        ]);
      });

      _showCase = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.white,
        child: SafeArea(
          bottom: false,
          child: Scaffold(
            appBar: PreferredSize(
                preferredSize: Size.fromHeight(250.dg),
                child: Container(
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: 25.0, right: 25.0, top: 10, bottom: 3),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            context.router.pop();
                          },
                          key: Key('back_button'),
                          child: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              color: Color(0xffFAF2DF),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.6),
                                  offset: Offset(4, 4),
                                  blurRadius: 10,
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(6.0),
                              child: Icon(
                                Icons.arrow_back_rounded,
                                color: Color(0xff30285D),
                                size: 35,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 25,
                        ),
                        Align(
                          alignment: Alignment.center,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Container(
                                      width: 60,
                                      child: SvgPicture.asset(
                                        'assets/logo/juno_logo_violet.svg',
                                        height: 35,
                                        color: AppTheme.primaryColor,
                                        fit: BoxFit.fitWidth,
                                      )),
                                  SizedBox(
                                    width: 5,
                                  ),
                                  Text(
                                    '+',
                                    style: GoogleFonts.poppins(
                                      color: AppTheme.primaryColor,
                                      fontSize: 30,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                'virtual remote',
                                style: GoogleFonts.poppins(
                                  color: AppTheme.primaryColor,
                                  fontSize: 14,
                                  fontStyle: FontStyle.italic,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Row(
                          children: [
                            VirtualBatteryIndicatorWidget(),
                            SizedBox(
                              width: 10,
                            ),
                            GestureDetector(
                              onTap: () {
                                context.router.push(HelpCenterHomeRoute());
                              },
                              child: Container(
                                height: 50,
                                width: 50,
                                decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withOpacity(0.6),
                                      offset: Offset(4, 4),
                                      blurRadius: 10,
                                    ),
                                  ],
                                ),
                                child: Showcase(
                                  key: _helpButtonKey,
                                  title: 'Help',
                                  description:
                                      'Press the help button to access the help center.',
                                  descTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  tooltipBorderRadius:
                                      BorderRadius.circular(20),
                                  tooltipBackgroundColor: Color(0xffFAF2DF),
                                  tooltipPadding: EdgeInsets.all(20),
                                  titleTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  targetShapeBorder: CircleBorder(),
                                  child: Padding(
                                    padding: const EdgeInsets.all(6.0),
                                    child: Icon(
                                      Icons.help_outline_sharp,
                                      color: Color(0xff30285D),
                                      size: 35,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20.0, vertical: 6),
                  child: Container(
                      width: .9.sw,
                      decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: Color(
                                  0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                              blurRadius: 4.0, // the blur radius
                              offset:
                                  Offset(0, 1), // the x,y offset of the shadow
                            ),
                          ],
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(32)),
                      child: Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Showcase(
                                  key: _modesExplainerKey,
                                  title: 'Modes',
                                  description:
                                      'The TENS unit has 3 modes: Pulse, Constant, and Burst. Pulse mode delivers a series of electrical pulses in a pattern, Constant mode delivers a continuous electrical pulse, and Burst mode delivers a series of electrical pulses in rapid succession. Each mode provides a different type of pain relief, so you can choose the one that works best for you.',
                                  targetBorderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(26),
                                  ),
                                  descTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  tooltipBorderRadius:
                                      BorderRadius.circular(20),
                                  tooltipBackgroundColor: Color(0xffFAF2DF),
                                  tooltipPadding: EdgeInsets.all(20),
                                  titleTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  child: Container(
                                    height: .125.sh,
                                    width: .5.sw - 13,
                                    decoration: BoxDecoration(
                                      color: Color(0xffFAF2DF),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(26),
                                      ),
                                    ),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          children: [
                                            SizedBox(
                                              width: .15.sw,
                                            ),
                                            Text(
                                              'Modes',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .copyWith(
                                                      color: Color(0xff26204A),
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      fontSize: 16),
                                            ),
                                            SizedBox(
                                              width: 10,
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                showDialog<void>(
                                                  context: context,
                                                  builder:
                                                      (BuildContext context) {
                                                    return AlertDialog(
                                                      backgroundColor:
                                                          Color(0xffFAF2DF),
                                                      title: Text(
                                                        'Modes',
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xff26204A),
                                                            fontSize: 20,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w500),
                                                      ),
                                                      content: Text(
                                                        'The TENS unit has 3 modes: Pulse, Constant, and Burst. Pulse mode delivers a series of electrical pulses in a pattern, Constant mode delivers a continuous electrical pulse, and Burst mode delivers a series of electrical pulses in rapid succession. Each mode provides a different type of pain relief, so you can choose the one that works best for you.',
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xff26204A),
                                                            fontSize: 16),
                                                      ),
                                                      actions: [
                                                        TextButton(
                                                          onPressed: () {
                                                            Navigator.of(
                                                                    context)
                                                                .pop();
                                                          },
                                                          child: Text('OK'),
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                );
                                              },
                                              child: Icon(
                                                Icons.info_outline,
                                                size: 23,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        VirtualTensMode()
                                      ],
                                    ),
                                  ),
                                ),
                                Showcase(
                                  key: _syncButtonKey,
                                  title: 'Sync',
                                  description:
                                      'Press the sync button to manually sync the device with the app.',
                                  descTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  tooltipBorderRadius:
                                      BorderRadius.circular(20),
                                  tooltipBackgroundColor: Color(0xffFAF2DF),
                                  tooltipPadding: EdgeInsets.all(20),
                                  titleTextStyle: GoogleFonts.roboto(
                                    color: AppTheme.primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  targetBorderRadius: BorderRadius.only(
                                    topRight: Radius.circular(26),
                                  ),
                                  child: Container(
                                    height: .125.sh,
                                    width: .4.sw - 13,
                                    decoration: BoxDecoration(
                                        color: Color(0xffFAF2DF),
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(26),
                                        )),
                                    child: Center(
                                        child: VirtualSyncButtonContainer()),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: .01.sh,
                            ),
                            Stack(
                              alignment: Alignment.center,
                              children: [
                                Positioned(
                                  top: 0,
                                  left: 0,
                                  child: Showcase(
                                    key: _tensExplainerKey,
                                    targetBorderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(32),
                                    ),
                                    title: 'TENS',
                                    description:
                                        'TENS (Transcutaneous Electrical Nerve Stimulation) uses gentle electrical pulses to help relieve pain and reduce muscle spasms. It has 10 intensity levels that can be adjusted using the + and - buttons. Please set the modes first before adjusting the intensity.',
                                    descTextStyle: GoogleFonts.roboto(
                                      color: AppTheme.primaryColor,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    tooltipBorderRadius:
                                        BorderRadius.circular(20),
                                    tooltipBackgroundColor: Color(0xffFAF2DF),
                                    tooltipPadding: EdgeInsets.all(20),
                                    titleTextStyle: GoogleFonts.roboto(
                                      color: AppTheme.primaryColor,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700,
                                    ),
                                    child: Container(
                                      width: .5.sw - 13,
                                      height: .685.sh,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          bottomLeft: Radius.circular(32),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          width: .5.sw - 13,
                                          height: .55.sh,
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Container(
                                                width: .48.sw,
                                                height: .55.sh,
                                                decoration: BoxDecoration(
                                                  color: Color(0xffFAF2DF),
                                                  borderRadius:
                                                      BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(26),
                                                  ),
                                                ),
                                                child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      SizedBox(
                                                        height: 15,
                                                      ),
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          SizedBox(
                                                            width: .11.sw,
                                                          ),
                                                          SvgPicture.asset(
                                                            'assets/remote/remote_charge.svg',
                                                            width: 23,
                                                            height: 23,
                                                          ),
                                                          SizedBox(
                                                            width: 10,
                                                          ),
                                                          GestureDetector(
                                                            onTap: () {
                                                              showDialog<void>(
                                                                context:
                                                                    context,
                                                                builder:
                                                                    (BuildContext
                                                                        context) {
                                                                  return AlertDialog(
                                                                    backgroundColor:
                                                                        Color(
                                                                            0xffFAF2DF),
                                                                    title: Text(
                                                                      'TENS',
                                                                      style: TextStyle(
                                                                          color: Color(
                                                                              0xff26204A),
                                                                          fontSize:
                                                                              20,
                                                                          fontWeight:
                                                                              FontWeight.w500),
                                                                    ),
                                                                    content:
                                                                        Text(
                                                                      'TENS (Transcutaneous Electrical Nerve Stimulation) is a type of therapy that uses gentle electrical pulses to help relieve pain and reduce muscle spasms. With 10 adjustable intensity levels, TENS can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                                      style: TextStyle(
                                                                          color: Color(
                                                                              0xff26204A),
                                                                          fontSize:
                                                                              16),
                                                                    ),
                                                                    actions: [
                                                                      TextButton(
                                                                        onPressed:
                                                                            () {
                                                                          Navigator.of(context)
                                                                              .pop();
                                                                        },
                                                                        child: Text(
                                                                            'OK'),
                                                                      ),
                                                                    ],
                                                                  );
                                                                },
                                                              );
                                                            },
                                                            child: Icon(
                                                              Icons
                                                                  .info_outline,
                                                              size: 23,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      RichText(
                                                          text: TextSpan(
                                                              text: 'TENS ',
                                                              style: Theme.of(
                                                                      context)
                                                                  .textTheme
                                                                  .bodyMedium!
                                                                  .copyWith(
                                                                      color: Color(
                                                                          0xff26204A),
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w400,
                                                                      fontSize:
                                                                          20),
                                                              children: [
                                                            TextSpan(
                                                                text:
                                                                    'Intensity',
                                                                style: Theme.of(
                                                                        context)
                                                                    .textTheme
                                                                    .bodyMedium!
                                                                    .copyWith(
                                                                        fontStyle:
                                                                            FontStyle
                                                                                .italic,
                                                                        color: Color(
                                                                            0xff26204A),
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w400,
                                                                        fontSize:
                                                                            16))
                                                          ])),
                                                      SizedBox(
                                                        height: 10,
                                                      ),
                                                      VirtualTensWidget(),
                                                      SizedBox(
                                                        height: 15,
                                                      ),
                                                    ]),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          width: .4.sw - 13,
                                          height: .55.sh,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.only(
                                              bottomRight: Radius.circular(26),
                                            ),
                                          ),
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Showcase(
                                                key: _heatExplainerKey,
                                                title: 'Heat',
                                                description:
                                                    'Heat therapy is a type of treatment that uses heat to relieve pain and stiffness. With 3 adjustable levels, heat therapy can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                descTextStyle:
                                                    GoogleFonts.roboto(
                                                  color: AppTheme.primaryColor,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                                tooltipBorderRadius:
                                                    BorderRadius.circular(20),
                                                tooltipBackgroundColor:
                                                    Color(0xffFAF2DF),
                                                tooltipPadding:
                                                    EdgeInsets.all(20),
                                                titleTextStyle:
                                                    GoogleFonts.roboto(
                                                  color: AppTheme.primaryColor,
                                                  fontSize: 20,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                                child: Container(
                                                  width: .4.sw - 13,
                                                  height: .415.sh,
                                                  decoration: BoxDecoration(
                                                    color: Color(0xffFAF2DF)
                                                        .withOpacity(.4),
                                                  ),
                                                  child: Column(
                                                    children: [
                                                      SizedBox(
                                                        height: 15,
                                                      ),
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: .16.sw,
                                                          ),
                                                          SvgPicture.asset(
                                                            'assets/remote/remote_heat.svg',
                                                            width: 23,
                                                            height: 23,
                                                          ),
                                                          SizedBox(
                                                            width: 10,
                                                          ),
                                                          GestureDetector(
                                                            onTap: () {
                                                              showDialog<void>(
                                                                context:
                                                                    context,
                                                                builder:
                                                                    (BuildContext
                                                                        context) {
                                                                  return AlertDialog(
                                                                    backgroundColor:
                                                                        Color(
                                                                            0xffFAF2DF),
                                                                    title: Text(
                                                                      'Heat',
                                                                      style: TextStyle(
                                                                          color: Color(
                                                                              0xff26204A),
                                                                          fontSize:
                                                                              20,
                                                                          fontWeight:
                                                                              FontWeight.w500),
                                                                    ),
                                                                    content:
                                                                        Text(
                                                                      'Heat therapy is a type of treatment that uses heat to relieve pain and stiffness. With 3 adjustable levels, heat therapy can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                                      style: TextStyle(
                                                                          color: Color(
                                                                              0xff26204A),
                                                                          fontSize:
                                                                              16),
                                                                    ),
                                                                    actions: [
                                                                      TextButton(
                                                                        onPressed:
                                                                            () {
                                                                          Navigator.of(context)
                                                                              .pop();
                                                                        },
                                                                        child: Text(
                                                                            'OK'),
                                                                      ),
                                                                    ],
                                                                  );
                                                                },
                                                              );
                                                            },
                                                            child: Icon(
                                                              Icons
                                                                  .info_outline,
                                                              size: 23,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(
                                                        height: 10,
                                                      ),
                                                      Text(
                                                        'Heat',
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium!
                                                            .copyWith(
                                                                color: Color(
                                                                    0xff26204A),
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                                fontSize: 20),
                                                      ),
                                                      SizedBox(
                                                        height: 10,
                                                      ),
                                                      Container(
                                                          child:
                                                              VirtualHeatWidget()),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Showcase(
                                                key: _playPauseExplainerKey,
                                                title: 'Play/Pause',
                                                description:
                                                    'You can pause and resume the therapy session by pressing the play/pause button.',
                                                descTextStyle:
                                                    GoogleFonts.roboto(
                                                  color: AppTheme.primaryColor,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                                tooltipBorderRadius:
                                                    BorderRadius.circular(20),
                                                tooltipBackgroundColor:
                                                    Color(0xffFAF2DF),
                                                tooltipPadding:
                                                    EdgeInsets.all(20),
                                                titleTextStyle:
                                                    GoogleFonts.roboto(
                                                  color: AppTheme.primaryColor,
                                                  fontSize: 20,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                                child: VirtualPlayPauseButton(),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      )),
                ),
              ],
            ),
          ),
        ));
  }
}
