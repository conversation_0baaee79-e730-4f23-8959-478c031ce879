import 'dart:io';
import 'package:authentication/domain/failure/auth_failure.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:authentication/authentication.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../routing/app_pages.gr.dart';

@RoutePage()
class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xffFAF2DF),
            Color.fromARGB(255, 247, 224, 255),
          ],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: false,
        appBar: CurvedAppBar(
          appBarColor:
              MediaQuery.of(context).platformBrightness == Brightness.dark
                  ? AppTheme.loginAppBarColor
                  : AppTheme.primaryColor,
          logoColor: MediaQuery.of(context).platformBrightness == Brightness.dark
              ? AppTheme.primaryColor
              : AppTheme.loginAppBarColor,
          height: .35.sw, // The height of your curved app bar
          topLeftIcon: IconButton(
            icon: Icon(
              Icons.arrow_back_rounded,
              color: MediaQuery.of(context).platformBrightness == Brightness.dark
                  ? Colors.black
                  : Colors.white,
            ),
            onPressed: () {
              context.router.pop();
            },
          ),
        ),
        body: SingleChildScrollView(
          reverse: true,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: .10.sw,
                width: 1.sw,
              ),
              SizedBox(
                height: 0.80.sw,
                width: .8.sw,
                child: BlocProvider(
                    create: (context) => getIt<SignInBloc>(),
                    child: EmailSignInWrapper(
                        child: Container(
                          width: .5.sw,
                          height: .12.sw,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25),
                            color: Theme.of(context).primaryColor,
                          ),
                          child: Center(
                              child: Text(
                            "Log In",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(fontSize: 20),
                          )),
                        ),
                        forgotPasswordChild: TextButton(
                          onPressed: () {
                            context.router.push(ResetPasswordRoute());
                          },
                          child: Container(
                              alignment: Alignment.topRight,
                              child: Text(
                                'Forgot your password?',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(
                                      color: Color(0xff30285D),
                                      decoration: TextDecoration.underline,
                                    ),
                              )),
                        ),
                        onRegisterSuccess: (user) {
                          user.isEmailVerified == true
                              ? user.isOnboarded == true
                                  ? context.router.push(HomeRoute())
                                  : context.router.push(GetStartedRoute())
                              : context.router.push(EmailVerificationRoute());
                        },
                        onRegisterFailure: (failure) {
                          print(failure.toString());
                        })),
              ),
              Container(
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(horizontal: 70),
                child: Row(
                  children: [
                    Expanded(
                      child: Divider(
                        color: Color(0xff554C9F),
                        thickness: 1,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Text(
                        "or",
                        style: TextStyle(color: Color(0xff554C9F)),
                      ),
                    ),
                    Expanded(
                      child: Divider(
                        color: Color(0xff554C9F),
                        thickness: 1,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Platform.isIOS
                      ? BlocProvider(
                          create: (context) => getIt<SignInBloc>(),
                          child: AppleSignInButtonWrapper(
                            buttonChild: Container(
                              width: 70,
                              height: 70,
                              margin: EdgeInsets.only(right: 25),
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0x40000000),
                                    blurRadius: 4.0,
                                    offset: Offset(0, 4),
                                  ),
                                ],
                              ),
                               child: SvgPicture.asset(
                                 'assets/social_icons/apple_social_icon.svg',
                                 height: 30,
                                 width: 30,
                               ),
                            ),
                            onSignInSuccess: (user) {
                              user.isOnboarded == true
                                  ? context.router.push(HomeRoute())
                                  : context.router.push(GetStartedRoute());
                            },
                            onSignInFailure: (failure) {},
                          ),
                        )
                      : Container(),
                  Container(
                    height: 70,
                    width: 70,
                    child: BlocProvider(
                      create: (context) => getIt<SignInBloc>(),
                      child: GoogleSignInButtonWrapper(
                        buttonChild: Container(
                          padding: EdgeInsets.all(18),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Color(0x40000000),
                                blurRadius: 4.0,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                           child:
                           SvgPicture.asset(
                             'assets/social_icons/google_social_icon.svg',
                             height: 30,
                             width: 30,
                           ),
                        ),
                        onSignInSuccess: (user) {
                          user.isOnboarded == true
                              ? context.router.push(HomeRoute())
                              : context.router.push(GetStartedRoute());
                        },
                        onSignInFailure: (failure) {
                          // Handle sign-in failure
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20),
              GestureDetector(
                onTap: () {
                  context.router.push(const SignUpRoute());
                },
                child: Container(
                  width: .7.sw,
                  height: .12.sw,
                  child: Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                            child: Text(
                          "Don't have an account? ",
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontSize: 16,
                              color: Theme.of(context).primaryColor),
                        )),
                        Container(
                          child: Text(
                            "Sign Up",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w800,
                                  decoration: TextDecoration.underline,
                                  decorationColor: Theme.of(context).primaryColor,
                                  color: Theme.of(context).primaryColor,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
