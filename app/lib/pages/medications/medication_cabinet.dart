// lib/pages/medications/medication_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:design_system/design/theme.dart';
import 'package:auto_route/annotations.dart';
import 'package:account_management/application/manage_medications_bloc/manage_medications_bloc.dart';
import 'package:account_management/application/medication_watcher_bloc/medication_watcher_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:account_management/domain/model/medication_model.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/medications/medication_cabinet_button.dart';
import 'package:juno_plus/pages/medications/medication_form.dart';
import 'package:uuid/uuid.dart';
import 'package:bluetooth/bluetooth.dart';
import '../../custom_widgets/curved_app_bar.dart';
import 'delete_medication_dialog.dart';
import 'package:intl/intl.dart';
import 'logged_time_widget.dart'; // Import the new widget

@RoutePage()
class MedicationCabinetPage extends StatelessWidget {
  const MedicationCabinetPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) => getIt<MedicationWatcherBloc>()
              ..add(const MedicationWatcherEvent.watchAllStarted())),
      ],
      child: MedicationViewPage(),
    );
  }
}

class MedicationViewPage extends StatefulWidget {
  const MedicationViewPage({super.key});

  @override
  State<MedicationViewPage> createState() => _MedicationViewPageState();
}

class _MedicationViewPageState extends State<MedicationViewPage> {
  void _showDeleteDialog(BuildContext context, MedicationModel medication) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BlocProvider(
          create: (context) => getIt<ManageMedicationsBloc>(),
          child: DeleteMedicationDialog(
            medication: medication,
          ),
        );
      },
    );
  }

  void _editMedication(BuildContext context, MedicationModel medication) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (buildContext) => Container(
            height: .85.sh,
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                topRight: Radius.circular(32),
              ),
            ),
            clipBehavior: Clip.hardEdge,
            child: MedicationFormPage(medication: medication)
        )
    );
  }


  Widget _buildMedicationItem(MedicationModel medication) {
    final format = DateFormat('hh:mm a');
    final sortedMedications = medication.timeofDay.toList()
      ..sort((a, b) => format.parse(a).compareTo(format.parse(b)));
    return GestureDetector(
      onTap: () => _editMedication(context, medication),
      child: Container(
        margin: EdgeInsets.only(bottom: 20),
        child: Slidable(
          key: ValueKey(medication.id),
          endActionPane: ActionPane(
          motion: const StretchMotion(),
          extentRatio: 0.35,
          children: [
            CustomSlidableAction(
              onPressed: (_) => _showDeleteDialog(context, medication),
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(50)),
              child: Container(
                height: 70,
                width: 70,
                decoration: BoxDecoration(
                  color: Color(0xffff9492),
                  shape: BoxShape.circle
                ),
                child: Icon(Icons.delete_outline_rounded, size: 30,),
              )
            ),
          ],
        ),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(32),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x40000000),
                    blurRadius: 4.0,
                    offset: Offset(0, 1),
                  ),
                ],
            ),
            constraints: BoxConstraints(minHeight: 0.15.sw, minWidth: 0.75.sw),
            child:
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 0.15.sw,
                      height: 0.15.sw,
                      margin: EdgeInsets.only(right: 10),
                      padding: EdgeInsets.only(top: 8),
                      decoration: BoxDecoration(
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x40000000),
                            blurRadius: 4.0,
                            offset: Offset(0, 3),
                          ),
                        ],
                        borderRadius: BorderRadius.circular(42),
                        color: Color(0xfffcecd9),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          'assets/home/<USER>',
                          height: 50,
                          width: 50,
                        ),
                      ),
                    ),

                    Container(
                      alignment: Alignment.topLeft,
                      constraints: BoxConstraints(minWidth: 185),
                       child:
                          Container(
                            margin: EdgeInsets.only(left: 10),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${medication.name!}',
                                  style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff30285D)),
                                ),
                                SizedBox(height: 3),
                                Text(
                                  '${medication.dosage!} ${medication.dosageUnit}, ${medication.frequencyUnit?[0].toUpperCase()}${medication.frequencyUnit?.substring(1)}',
                                  style: TextStyle(
                                      color: Color(0xff30285D).withOpacity(0.6)),
                                ),
                                SizedBox(height: 3),
                                Container(
                                  width: 0.54.sw,
                                  child: Wrap(
                                    spacing: 8, // space between containers horizontally
                                    runSpacing: 5, // space between rows if wrapped
                                    children: sortedMedications.map<Widget>((time) {
                                      return Container(
                                        padding: EdgeInsets.symmetric(horizontal: 3, vertical: 3),
                                        decoration: BoxDecoration(
                                          color: Color(0xffDDDAF5), // light purple
                                          borderRadius: BorderRadius.circular(5),
                                        ),
                                        child: Text(
                                          time,
                                          style: TextStyle(
                                            color: Color(0xff30285D),
                                            fontWeight: FontWeight.w500,
                                            fontSize: 14
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                                SizedBox(height: 3),
                                medication.notes != ""
                                    ? Container(
                                      width: 0.4.sw,
                                      child: Text(
                                        '${medication.notes}',
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                        style: TextStyle(color: Color(0xff30285D)),
                                      ),
                                    )
                                    : SizedBox.shrink(),
                              ],
                            ),
                          ),
                        //],
                     // ),
                    ),
                    RotatedBox(quarterTurns: 1,child: Icon(Icons.drag_handle, color: Colors.black45,))
                  ],
                ),
              //],
            //),
          ),
        ),
      ),
    );
  }

  Future<void> openDialog(BuildContext context) async {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Color(0xffFAF2DF),
        builder: (buildContext) => Container(
            height: .82.sh,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xffFAF2DF),
                  Color.fromARGB(255, 247, 224, 255),
                ],
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                topRight: Radius.circular(32),
              ),
            ),
            clipBehavior: Clip.hardEdge,
            child: MedicationFormPage(medication: null)
        )
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MedicationWatcherBloc, MedicationWatcherState>(
        builder: (context, state) {
          if (state is Loading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is Success) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xffFAF2DF),
                    Color.fromARGB(255, 247, 224, 255),
                  ],
                ),
              ),
              child: Scaffold(
                extendBodyBehindAppBar: true,
                backgroundColor: Colors.transparent,
                appBar: CurvedAppBar(
                  appBarColor: AppTheme.primaryColor,
                  logoColor: const Color(0xffFAF2DF),
                  height: .35.sw,
                  topLeftIcon:IconButton(
                    icon: const Icon(Icons.arrow_back,color: Colors.white,),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                  // The height of your curved app bar
                ),
                body: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(22.0),
                    child: Column(
                      children: [
                        SizedBox(
                          height: .35.sw,
                        ),
                        Container(
                          alignment: Alignment.center,
                          width: 1.sw,
                          height: 0.20.sw,
                          child: const Text(
                            "My Medicine Cabinet",
                            style: TextStyle(color: Colors.black, fontSize: 25),
                          ),
                        ),
                        state.medications.isEmpty
                            ? const Text("No medications added yet!\n", style: TextStyle(color: Colors.black),)
                            : ListView.builder(
                          padding: EdgeInsets.only(top: 20),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: state.medications.length,
                          itemBuilder: (context, index) {
                            return _buildMedicationItem(
                                state.medications[index]);
                          },
                        ),
                        Container(
                          padding: const EdgeInsets.fromLTRB(30, 0, 0, 0),
                          alignment: Alignment.centerLeft,
                          width: 1.sw,
                          height: 0.18.sw,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(30)),
                            boxShadow: [
                              BoxShadow(
                                color: Color(
                                    0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                blurRadius: 4.0, // the blur radius
                                offset:
                                Offset(0, 3), // the x,y offset of the shadow
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              const Text(
                                "Add Medication",
                                style: TextStyle(
                                  color: Colors.black,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(110, 0, 0, 0),
                                child: ElevatedButton(
                                  key: const Key('add_medication_button'),
                                  style: ButtonStyle(
                                    backgroundColor: WidgetStatePropertyAll<Color>(AppTheme.primaryColor),
                                  ),
                                  onPressed: () {
                                    openDialog(context);
                                  },
                                  child: const Text(
                                    '+',
                                    style: TextStyle(
                                        fontSize: 40, fontWeight: FontWeight.w300, color: Colors.white),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          } else if (state is LoadFailure) {
            return const Center(child: Text('Failed to load medications'));
          }
          return const Center(child: Text('Something went wrong'));
        },
    );
  }
}

