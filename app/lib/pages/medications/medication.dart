import 'package:account_management/domain/model/daily_medication_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:design_system/design/theme.dart';
import 'package:auto_route/annotations.dart';
import 'package:account_management/application/daily_medication_bloc/daily_medication_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/medications/medication_cabinet_button.dart';
import '../../custom_widgets/curved_app_bar.dart';
import 'package:timeline_tile/timeline_tile.dart';

@RoutePage()
class MedicationPage extends StatelessWidget {
  const MedicationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<DailyMedicationBloc>()
            ..add(const DailyMedicationEvent.watchStarted()),
        ),
      ],
      child: const MedicationViewPage(),
    );
  }
}

class MedicationViewPage extends StatefulWidget {
  const MedicationViewPage({super.key});

  @override
  State<MedicationViewPage> createState() => _MedicationViewPageState();
}

class _MedicationViewPageState extends State<MedicationViewPage> {

  @override
  Widget build(BuildContext context) {
    return FloatingMedicationCabinetButton(
      child: BlocBuilder<DailyMedicationBloc, DailyMedicationState>(
      builder: (context, state) {
        return state.map(
          initial: (_) => const Center(child: Text('Loading...')),
          loadInProgress: (_) => const Center(child: CircularProgressIndicator()),
          loadFailure: (f) => Center(child: Text('Error loading medications.')),
          loadSuccess: (s) {
            final meds = s.medications;
            final groupedMeds = _groupMedicationsByTime(meds);
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xffFAF2DF),
                    Color.fromARGB(255, 247, 224, 255),
                  ],
                ),
              ),
              child: Scaffold(
                extendBodyBehindAppBar: true,
                backgroundColor: Colors.transparent,
                appBar: CurvedAppBar(
                  appBarColor: AppTheme.primaryColor,
                  logoColor: const Color(0xffFAF2DF),
                  height: .35.sw,
                  topLeftIcon: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
                body: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(22.0),
                    child: Column(
                      children: [
                        SizedBox(
                          height: .35.sw,
                        ),
                        // Title section
                        Container(
                          alignment: Alignment.center,
                          width: 1.sw,
                          height: 0.20.sw,
                          child: const Text(
                            "Today's Medications",
                            style: TextStyle(color: Colors.black, fontSize: 25),
                          ),
                        ),

                        // If no medications available
                        if (meds.isEmpty)
                          Column(
                            children: [
                              const Text("No medications today. \n", style: TextStyle(color: Colors.black)),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Text("Access Your  ", style: TextStyle(color: Colors.black)),
                                  Icon(Icons.medical_services_rounded, color: AppTheme.primaryColor),
                                  const Text(" Medicine Cabinet", style: TextStyle(color: Colors.black)),
                                ],
                              ),
                              const Text("to add a new medication.", style: TextStyle(color: Colors.black)),
                            ],
                          )
                        // If there are medications available
                        else
                        // Using a ListView.builder inside a Column to avoid layout issues
                          Container(
                            child: ListView.builder(
                              padding: EdgeInsets.only(top: 20),
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: groupedMeds.length,
                              itemBuilder: (context, index) {
                                final group = groupedMeds[index];
                                final isFirst = index == 0;
                                final isLast = index == groupedMeds.length - 1;
                                return _buildMedicationItem(group, isFirst, isLast);
                              },
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
      ),
    );
  }

  TimeOfDay parseTimeOfDay(String timeString) {
    final format = DateFormat("h:mm a");
    final dateTime = format.parse(timeString);
    return TimeOfDay.fromDateTime(dateTime);
  }

  String formatTimeOfDay(TimeOfDay time) {
    final now = DateTime.now();
    final dt = DateTime(now.year, now.month, now.day, time.hour, time.minute);
    return DateFormat('h:mm a').format(dt);
  }

  List<List<DailyMedicationModel>> _groupMedicationsByTime(
      List<DailyMedicationModel> medications) {
    Map<String, List<DailyMedicationModel>> grouped = {};

    for (var med in medications) {
      final timeKey = timeOfDayToString(med.time!);

      if (grouped.containsKey(timeKey)) {
        grouped[timeKey]!.add(med);
      } else {
        grouped[timeKey] = [med];
      }
    }

    return grouped.values.toList();
  }

  String timeOfDayToString(TimeOfDay tod) {
    final hour = tod.hour.toString().padLeft(2, '0');
    final minute = tod.minute.toString().padLeft(2, '0');
    return '$hour:$minute'; // Change to AM/PM if needed
  }

  Widget _buildMedicationCard(DailyMedicationModel med) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(40),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
      ),
      constraints: BoxConstraints(minHeight: 0.10.sw, minWidth: 0.75.sw),
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child:
      Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 0.14.sw,
            height: 0.14.sw,
            padding: EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              boxShadow: const [
                BoxShadow(
                  color: Color(0x40000000),
                  blurRadius: 4.0,
                  offset: Offset(0, 3),
                ),
              ],
              borderRadius: BorderRadius.circular(42),
              color: Color(0xfffcecd9),
            ),
            child: Center(
              child: SvgPicture.asset(
                'assets/home/<USER>',
                height: 50,
                width: 50,
              ),
            ),
          ),
          Container(
            alignment: Alignment.topLeft,
            constraints: BoxConstraints(minWidth: 185),
            child:
            Container(
              margin: EdgeInsets.only(left: 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${med.medName}',
                    style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Color(0xff30285D)),
                  ),
                  SizedBox(height: 3),
                  Row(
                    children: [
                      Text(
                        '${med.medDosage} ${med.medDosageUnit}',
                        style: TextStyle(
                            color: Color(0xff30285D).withOpacity(0.6)),
                      ),
                      if (med.loggedTime != null) ...[
                        SizedBox(width: 8), // a little space
                        Container(
                          width: 70,
                          height: 25,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              color: Color(0xFFFFD277),
                              borderRadius: BorderRadius.circular(5)
                          ),
                          child: Text(
                            formatTimeOfDay(med.loggedTime!),
                            style: TextStyle(
                                color: Colors.black,
                                fontSize: 15
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  med.medNotes != ""
                      ? Container(
                    width: 0.4.sw,
                    child: Text(
                      '${med.medNotes}',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: TextStyle(color: Color(0xff30285D)),
                    ),
                  )
                      : SizedBox.shrink(),
                ],
              ),
            ),
          ),
          Container(
            child: ToggleCheckWidget(
              loggedTime: med.loggedTime,
              onLoggedTime: (TimeOfDay newTime) {
                setState(() {
                  med.loggedTime = newTime;
                });
              },
              checkedOff: () {
                setState(() {
                  med.loggedTime = null;
                });
              },
            )
          )
        ],
      ),
    );
  }

  Widget _buildMedicationItem(List<DailyMedicationModel> meds, bool isFirst,
      bool isLast) {
    bool _isPast(TimeOfDay time) {
      final now = TimeOfDay.now();
      return time.hour < now.hour || (time.hour == now.hour && time.minute < now.minute);
    }
    return TimelineTile(
      alignment: TimelineAlign.manual,
      lineXY: 0.0,
      isFirst: isFirst,
      isLast: isLast,
      indicatorStyle: IndicatorStyle(
        indicatorXY: 0.0,
        width: 25,
        color: _isPast(meds.first.time!) ? AppTheme.primaryColor : const Color(0xFFDDDAF5),
        padding: const EdgeInsets.only(right: 10),
      ),
      afterLineStyle: LineStyle(
        color: _isPast(meds.first.time!) ? AppTheme.primaryColor : const Color(0xFFDDDAF5),
        thickness: 3,
      ),
      endChild: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            formatTimeOfDay(meds.first.time!),
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          ...meds.map((med) =>
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: _buildMedicationCard(med),
              )),
        ],
      ),
    );
  }
}

class ToggleCheckWidget extends StatefulWidget {
  final TimeOfDay? loggedTime;
  final ValueChanged<TimeOfDay> onLoggedTime;
  final VoidCallback checkedOff;

  const ToggleCheckWidget({
    Key? key,
    required this.loggedTime,
    required this.onLoggedTime,
    required this.checkedOff,
  }) : super(key: key);

  @override
  _ToggleCheckWidgetState createState() => _ToggleCheckWidgetState();
}

class _ToggleCheckWidgetState extends State<ToggleCheckWidget> {
  bool get isChecked => widget.loggedTime != null;

  Future<void> _handleTap() async {
    if (isChecked) {
      widget.checkedOff();
    } else {
      final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );
      if (picked != null) {
        widget.onLoggedTime(picked);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isChecked ? const Color(0xFFFFB854) : const Color(0xFFDDDAF5),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.check_rounded,
          color: isChecked ? Colors.white : Colors.grey[700],
          size: 30,
        ),
      ),
    );
  }
}



