import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../custom_widgets/menstural_cycle_dial.dart';
import 'package:google_fonts/google_fonts.dart';

class MenstrualCycleDial extends StatelessWidget {
  final DateTime? selectedDate;

  const MenstrualCycleDial({
    super.key,
    this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    return MenstrualCycleDialWidget(selectedDate: selectedDate);
  }
}

class MenstrualCycleDialWidget extends StatelessWidget {
  final DateTime? selectedDate;

  const MenstrualCycleDialWidget({
    super.key,
    this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MenstrualCycleBloc, MenstrualCycleState>(
      builder: (context, state) {
        return state.maybeMap(
          loading: (_) => Container(
              height: 240,
              width: 240,
              child: const Center(child: CircularProgressIndicator())),
          data: (state) => MenstrualCycleTracker(
            cycleDays: state.cycleLength,
            periodDays: state.periodDays,
            currentDay: state.currentCycleDay,
            ovulationDays: state.ovulationDaysLength,
            ovulationDayStart: state.ovulationDayStart,
            selectedDate: selectedDate,
            size: 240,
            ovulationArcColor: const Color(0xffECA83D),
            textStyle: GoogleFonts.mulish(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
            outerCircleColor: const Color(0xffD3CFC6),
            periodArcColor: Theme.of(context).primaryColor,
            innerCircleColor: Theme.of(context).primaryColor,
            pointerColor:
                Theme.of(context).primaryColor, // Same as inner circle
          ),
          orElse: () => const SizedBox(),
        );
      },
    );
  }
}
