import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:analytics/analytics.dart';
import 'package:juno_plus/custom_widgets/curved_app_bar.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';

@RoutePage()
class TherapyAnalyticsChartPage extends StatefulWidget {
  @override
  _TherapyAnalyticsChartPageState createState() =>
      _TherapyAnalyticsChartPageState();
}

class _TherapyAnalyticsChartPageState extends State<TherapyAnalyticsChartPage> {
  final Color tensColor = const Color(0xffD46464);
  final Color heatColor = const Color(0xffF4BA4A);
  final Color painColor = const Color(0xff3B7EFF);

  final double width = 8;
  final double groupSpace = 10;

  @override
  void initState() {
    super.initState();
    // Load weekly data by default
    context
        .read<TherapyAnalyticsBloc>()
        .add(const TherapyAnalyticsEvent.loadWeeklyData());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        height: .35.sw,
        topLeftIcon: IconButton(
          icon: Icon(Icons.arrow_back_rounded, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: BlocBuilder<TherapyAnalyticsBloc, TherapyAnalyticsState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: .35.sw),
                  _buildDescription(),
                  SizedBox(height: 16),
                  _buildMainPageAverages(state),
                  SizedBox(height: 20),
                  _buildChartContainer(state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDescription() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: RichText(
        text: TextSpan(
          text: 'The charts show your last 5 therapy sessions with ',
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: Color(0xff30285D),
              ),
          children: [
            TextSpan(
              text: 'TENS',
              style: TextStyle(
                color: tensColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextSpan(text: ', '),
            TextSpan(
              text: 'Heat',
              style: TextStyle(
                color: heatColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextSpan(text: ', and '),
            TextSpan(
              text: 'Pain',
              style: TextStyle(
                color: painColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextSpan(text: ' levels (before and after therapy).'),
          ],
        ),
      ),
    );
  }

  Widget _buildMainPageAverages(TherapyAnalyticsState state) {
    return state.when(
      initial: () => Container(),
      loading: () => Container(),
      loaded: (chartData, startDate, endDate, viewType) {
        if (chartData.isEmpty) return Container();

        // Filter out empty sessions and take only valid sessions
        final validSessions = chartData
            .where((data) =>
                data.tensLevel > 0 ||
                data.heatLevel > 0 ||
                (data.painLevelBefore != null && data.painLevelBefore! > 0) ||
                (data.painLevelAfter != null && data.painLevelAfter! > 0))
            .take(5)
            .toList();

        if (validSessions.isEmpty) return Container();

        // Calculate averages
        final avgTens = validSessions.isEmpty
            ? 0
            : (validSessions.map((d) => d.tensLevel).reduce((a, b) => a + b) /
                    validSessions.length)
                .round();
        final avgHeat = validSessions.isEmpty
            ? 0
            : (validSessions.map((d) => d.heatLevel).reduce((a, b) => a + b) /
                    validSessions.length)
                .round();
        final painValues = validSessions
            .where((d) => d.painLevelAfter != null)
            .map((d) => d.painLevelAfter!)
            .toList();
        final avgPain = painValues.isEmpty
            ? 0
            : (painValues.reduce((a, b) => a + b) / painValues.length).round();

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          margin: EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            color: Color(0xffFAF2DF),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Color(0x20000000),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildMainAverageItem(
                  'TENS', avgTens.toString(), tensColor, Icons.flash_on),
              _buildMainAverageItem(
                  'Heat', avgHeat.toString(), heatColor, Icons.whatshot),
              _buildMainAverageItem(
                  'Pain', avgPain.toString(), painColor, Icons.healing),
            ],
          ),
        );
      },
      error: (message) => Container(),
    );
  }

  Widget _buildMainAverageItem(
      String label, String value, Color color, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 28,
            color: color,
          ),
        ),
        SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Color(0xff30285D).withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildChartContainer(TherapyAnalyticsState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Column(
        children: [
          _buildSingleChart(state, 'TENS Levels', tensColor, 'tens'),
          SizedBox(height: 20),
          _buildSingleChart(state, 'Heat Levels', heatColor, 'heat'),
          SizedBox(height: 20),
          _buildPainChart(state),
        ],
      ),
    );
  }

  Widget _buildSingleChart(
      TherapyAnalyticsState state, String title, Color color, String dataType) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        boxShadow: [
          BoxShadow(
            color: Color(0x20000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
        borderRadius: BorderRadius.all(Radius.circular(32)),
      ),
      width: 1.sw,
      height: .65.sw,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xff30285D),
              ),
            ),
            SizedBox(height: 16),
            Expanded(child: _buildSingleBarChart(state, dataType, color)),
          ],
        ),
      ),
    );
  }

  Widget _buildPainChart(TherapyAnalyticsState state) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        boxShadow: [
          BoxShadow(
            color: Color(0x20000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
        borderRadius: BorderRadius.all(Radius.circular(32)),
      ),
      width: 1.sw,
      height: .65.sw,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Text(
              'Pain Levels (Before & After)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xff30285D),
              ),
            ),
            SizedBox(height: 16),
            Expanded(child: _buildPainBarChart(state)),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LegendItem(color: painColor, text: 'Before'),
                SizedBox(width: 20),
                LegendItem(
                    color: painColor.withValues(alpha: 0.6), text: 'After'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Removed unused _buildChart method

  // Removed unused _buildDataSummary and _buildSummaryItem methods

  Widget _buildSingleBarChart(
      TherapyAnalyticsState state, String dataType, Color color) {
    return state.when(
      initial: () => Center(child: Text('Loading...')),
      loading: () => Center(child: CircularProgressIndicator()),
      loaded: (chartData, startDate, endDate, viewType) {
        if (chartData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.bar_chart_outlined,
                  size: 48,
                  color: Color(0xff30285D).withValues(alpha: 0.3),
                ),
                SizedBox(height: 16),
                Text(
                  'No therapy sessions yet',
                  style: TextStyle(
                    color: Color(0xff30285D),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Start using your device to track your progress',
                  style: TextStyle(
                    color: Color(0xff30285D).withValues(alpha: 0.6),
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        return _buildSingleTypeBarChart(chartData, dataType, color);
      },
      error: (message) => Center(
        child: Text(
          'Error: $message',
          style: TextStyle(color: Colors.red),
        ),
      ),
    );
  }

  Widget _buildPainBarChart(TherapyAnalyticsState state) {
    return state.when(
      initial: () => Center(child: Text('Loading...')),
      loading: () => Center(child: CircularProgressIndicator()),
      loaded: (chartData, startDate, endDate, viewType) {
        if (chartData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.healing_outlined,
                  size: 48,
                  color: Color(0xff30285D).withValues(alpha: 0.3),
                ),
                SizedBox(height: 16),
                Text(
                  'No pain data yet',
                  style: TextStyle(
                    color: Color(0xff30285D),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Complete therapy sessions with feedback to see pain trends',
                  style: TextStyle(
                    color: Color(0xff30285D).withValues(alpha: 0.6),
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        return _buildPainComparisonChart(chartData);
      },
      error: (message) => Center(
        child: Text(
          'Error: $message',
          style: TextStyle(color: Colors.red),
        ),
      ),
    );
  }

  Widget _buildSingleTypeBarChart(
      List<TherapyChartDataModel> chartData, String dataType, Color color) {
    final barGroups = chartData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;

      double value = 0;
      switch (dataType) {
        case 'tens':
          value = data.tensLevel;
          break;
        case 'heat':
          value = data.heatLevel;
          break;
      }

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: value,
            color: color,
            width: width,
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        groupsSpace: groupSpace,
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) =>
                  _buildSessionBottomTitles(value, meta, chartData),
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) =>
                  _buildLeftTitles(value, meta, dataType),
              reservedSize: 50,
              interval: dataType == 'heat'
                  ? 1
                  : 2, // Heat: show 0,1,2,3; TENS: show 0,2,4,6,8,10
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false, reservedSize: 0),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: barGroups,
        gridData: FlGridData(show: false),
        maxY: dataType == 'heat'
            ? 4
            : 10, // Heat: 1-3 levels (maxY=4 for proper spacing), TENS: 0-10
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => Colors.black87,
            tooltipRoundedRadius: 8,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              if (groupIndex >= chartData.length) return null;

              final data = chartData[groupIndex];
              return BarTooltipItem(
                '${dataType.toUpperCase()}: ${rod.toY.toStringAsFixed(1)}\n${data.sessionLabel}\n${data.sessionDurationMinutes} min',
                TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildPainComparisonChart(List<TherapyChartDataModel> chartData) {
    final barGroups = chartData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;

      return BarChartGroupData(
        barsSpace: 2,
        x: index,
        barRods: [
          BarChartRodData(
            toY: data.painLevelBefore ?? 0,
            color: painColor,
            width: width,
          ),
          BarChartRodData(
            toY: data.painLevelAfter ?? 0,
            color: painColor.withValues(alpha: 0.6),
            width: width,
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        groupsSpace: groupSpace,
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) =>
                  _buildSessionBottomTitles(value, meta, chartData),
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: leftTitles,
              reservedSize: 50,
              interval: 2,
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false, reservedSize: 0),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: barGroups,
        gridData: FlGridData(show: false),
        maxY: 10, // Pain levels go up to 10
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => Colors.black87,
            tooltipRoundedRadius: 8,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              if (groupIndex >= chartData.length) return null;

              final data = chartData[groupIndex];
              final label = rodIndex == 0 ? 'Before' : 'After';
              final value =
                  rodIndex == 0 ? data.painLevelBefore : data.painLevelAfter;

              return BarTooltipItem(
                'Pain $label: ${value?.toStringAsFixed(1) ?? 'N/A'}\n${data.sessionLabel}',
                TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSessionBottomTitles(
      double value, TitleMeta meta, List<TherapyChartDataModel> chartData) {
    if (value.toInt() >= chartData.length) return Container();

    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        'S${value.toInt() + 1}', // S1, S2, S3, etc.
        style: const TextStyle(
          color: Color(0xff7589a2),
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildLeftTitles(double value, TitleMeta meta, String dataType) {
    // For heat levels, show 0, 1, 2, 3
    // For TENS levels, show 0, 2, 4, 6, 8, 10
    if (dataType == 'heat' && value > 3) return Container();
    if (dataType != 'heat' && value > 10) return Container();

    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        value.toInt().toString(),
        style: const TextStyle(
          color: Color(0xff7589a2),
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  // Removed unused _buildBarChart method

  // Removed unused _buildLegend method

  Widget leftTitles(double value, TitleMeta meta) {
    const style = TextStyle(
      color: Color(0xff7589a2),
      fontWeight: FontWeight.bold,
      fontSize: 14,
    );
    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 16,
      child: Text(value.toInt().toString(), style: style),
    );
  }

  Widget bottomTitles(
      double value, TitleMeta meta, List<TherapyChartDataModel> chartData) {
    if (value.toInt() >= chartData.length) return Container();

    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(
        chartData[value.toInt()].sessionLabel,
        style: const TextStyle(
          color: Color(0xff7589a2),
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  // Removed unused makeGroupData method
}

class LegendItem extends StatelessWidget {
  final Color color;
  final String text;

  const LegendItem({required this.color, required this.text, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 4),
        Text(text, style: TextStyle(color: Color(0xff26204A), fontSize: 14)),
      ],
    );
  }
}

class TherapyAnalyticsChartMiniWidget extends StatefulWidget {
  const TherapyAnalyticsChartMiniWidget({super.key});

  @override
  State<TherapyAnalyticsChartMiniWidget> createState() =>
      _TherapyAnalyticsChartMiniWidgetState();
}

class _TherapyAnalyticsChartMiniWidgetState
    extends State<TherapyAnalyticsChartMiniWidget> {
  final Color tensColor = const Color(0xffD46464);
  final Color heatColor = const Color(0xffF4BA4A);
  final Color painColor = const Color(0xff3B7EFF);
  final double width = 7;

  @override
  void initState() {
    super.initState();
    // Load weekly data for mini widget
    context
        .read<TherapyAnalyticsBloc>()
        .add(const TherapyAnalyticsEvent.loadWeeklyData());
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: .65.sw,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Average values display
          BlocBuilder<TherapyAnalyticsBloc, TherapyAnalyticsState>(
            builder: (context, state) {
              return state.when(
                initial: () => Container(),
                loading: () => Container(),
                loaded: (chartData, startDate, endDate, viewType) {
                  if (chartData.isEmpty) return Container();
                  return _buildAverageValues(chartData);
                },
                error: (message) => Container(),
              );
            },
          ),
          // Chart
          Expanded(
            child: BlocBuilder<TherapyAnalyticsBloc, TherapyAnalyticsState>(
              builder: (context, state) {
                return state.when(
                  initial: () => Center(child: CircularProgressIndicator()),
                  loading: () => Center(child: CircularProgressIndicator()),
                  loaded: (chartData, startDate, endDate, viewType) {
                    if (chartData.isEmpty) {
                      return _buildVirtualExperienceButton();
                    }
                    return _buildMiniBarChart(chartData); // Show session data
                  },
                  error: (message) => Center(
                    child: Icon(Icons.error, color: Colors.red, size: 16),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAverageValues(List<TherapyChartDataModel> chartData) {
    // Filter out empty sessions and take only valid sessions
    final validSessions = chartData
        .where((data) =>
            data.tensLevel > 0 ||
            data.heatLevel > 0 ||
            (data.painLevelBefore != null && data.painLevelBefore! > 0) ||
            (data.painLevelAfter != null && data.painLevelAfter! > 0))
        .take(5)
        .toList();

    if (validSessions.isEmpty) return Container();

    // Calculate averages as integers
    final avgTens = validSessions.isEmpty
        ? 0
        : (validSessions.map((d) => d.tensLevel).reduce((a, b) => a + b) /
                validSessions.length)
            .round();
    final avgHeat = validSessions.isEmpty
        ? 0
        : (validSessions.map((d) => d.heatLevel).reduce((a, b) => a + b) /
                validSessions.length)
            .round();
    final painValues = validSessions
        .where((d) => d.painLevelAfter != null)
        .map((d) => d.painLevelAfter!)
        .toList();
    final avgPain = painValues.isEmpty
        ? 0
        : (painValues.reduce((a, b) => a + b) / painValues.length).round();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildMiniSummaryItem('TENS', avgTens.toString(), tensColor),
              //divider line
              Container(
                width: 1,
                height: 30,
                color: Color(0xff30285D).withValues(alpha: 0.2),
              ),
              _buildMiniSummaryItem('Heat', avgHeat.toString(), heatColor),
              Container(
                width: 1,
                height: 30,
                color: Color(0xff30285D).withValues(alpha: 0.2),
              ),
              _buildMiniSummaryItem('Pain', avgPain.toString(), painColor),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMiniSummaryItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Color(0xff30285D).withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildVirtualExperienceButton() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Virtual\nExperience',
                  style: TextStyle(
                    color: Color(0xff26204a),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Spacer(),
                Container(
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: Icon(Icons.arrow_forward_rounded,
                        color: Colors.white, size: 18),
                    onPressed: () {
                      // Navigate to virtual remote page
                      context.router.push(VirtualRemoteRoute());
                    },
                  ),
                ),

              ],
            ),
            SizedBox(height: 4),
            Text(
              'Experience the device virtually and explore its features.',
              style: TextStyle(
                color: Colors.grey,
                fontWeight: FontWeight.w400,
                fontSize: 13,
              ),
            ),
          ],
        ),

        SizedBox(height: 8),
        Image.asset(
          'assets/product_showcase/juno_phone.png',
          width: 0.25.sw,
          fit: BoxFit.fitWidth,
        ),
      ],
    );
  }

  Widget _buildMiniBarChart(List<TherapyChartDataModel> chartData) {
    // Filter out empty sessions and take only valid sessions
    final validSessions = chartData
        .where((data) =>
            data.tensLevel > 0 ||
            data.heatLevel > 0 ||
            (data.painLevelBefore != null && data.painLevelBefore! > 0) ||
            (data.painLevelAfter != null && data.painLevelAfter! > 0))
        .take(5)
        .toList();

    if (validSessions.isEmpty) {
      return _buildVirtualExperienceButton();
    }

    final barGroups = validSessions.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;

      return BarChartGroupData(
        barsSpace: 1,
        x: index,
        barRods: [
          BarChartRodData(
            toY: data.tensLevel,
            color: tensColor,
            width: 6,
          ),
          BarChartRodData(
            toY: data.heatLevel,
            color: heatColor,
            width: 6,
          ),
          BarChartRodData(
            toY: data.painLevelAfter ?? 0,
            color: painColor,
            width: 6,
          ),
        ],
      );
    }).toList();

    // Calculate dynamic maxY based on data (Heat levels are handled separately with max 3)
    double maxTens =
        validSessions.map((d) => d.tensLevel).reduce((a, b) => a > b ? a : b);
    double maxPain = validSessions
        .where((d) => d.painLevelAfter != null)
        .map((d) => d.painLevelAfter!)
        .fold(0.0, (a, b) => a > b ? a : b);

    // Use appropriate max values: TENS=10, Pain=10 (Heat is scaled separately)
    double maxY = [maxTens, maxPain].reduce((a, b) => a > b ? a : b);
    maxY = maxY < 10 ? 10 : maxY + 1; // Ensure minimum scale of 10

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceEvenly,
        groupsSpace: 8,
        titlesData: FlTitlesData(
          show: false, // Remove all labels
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false, reservedSize: 0),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false, reservedSize: 0),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false, reservedSize: 0),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false, reservedSize: 0),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: barGroups,
        gridData: FlGridData(show: false),
        maxY: maxY,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => Colors.black87,
            tooltipRoundedRadius: 4,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              if (groupIndex >= validSessions.length) return null;

              final data = validSessions[groupIndex];
              String label = '';
              String value = '';

              switch (rodIndex) {
                case 0:
                  label = 'TENS';
                  value = data.tensLevel.toStringAsFixed(0);
                  break;
                case 1:
                  label = 'Heat';
                  value = data.heatLevel.toStringAsFixed(0);
                  break;
                case 2:
                  label = 'Pain';
                  value = data.painLevelAfter?.toStringAsFixed(0) ?? '0';
                  break;
              }

              return BarTooltipItem(
                '$label: $value\n${data.sessionLabel}',
                TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
