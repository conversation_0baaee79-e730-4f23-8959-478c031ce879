import 'package:flutter/material.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';


class Help extends StatelessWidget {
  final String? why;
  final String? what;

  const Help({Key? key, this.why, this.what}) : super(key: key);

  void _showHelpDialog(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => _HelpDialog(why: why, what: what),
    );
  }

  @override
  Widget build(BuildContext context) {
     return GestureDetector(
        onTap: () => _showHelpDialog(context),
       child: Container(
           width: 108,
           height: .11.sw,
           decoration: BoxDecoration(
               color: AppTheme.primaryColor,
               boxShadow: [
                 BoxShadow(
                   color: Color(0x40000000),
                   // #00000040 in CSS corresponds to 0x40000000 in Flutter
                   blurRadius: 4.0,
                   // the blur radius
                   offset: Offset(0,
                       1), // the x,y offset of the shadow
                 ),
               ],
               borderRadius: BorderRadius.all(
                   Radius.circular(32))),
           child: Padding(
             padding: const EdgeInsets.all(5.0),
             child: Row(
               children: [
                 Container(
                   height: 40,
                   width: 40,
                   decoration: BoxDecoration(
                     color: Color(0xffE9DEFD),
                     shape: BoxShape.circle,
                   ),
                   child: Padding(
                     padding:
                     const EdgeInsets.all(6.0),
                     child: SvgPicture.asset(
                       'assets/home/<USER>',
                       height: 30,
                       width: 30,
                       colorFilter: ColorFilter.mode(
                         Color(0xff30285D),
                         BlendMode.srcIn,
                       ),
                     ),
                   ),
                 ),
                 SizedBox(
                   width: 5,
                 ),
                 Text(
                   'Help',
                   style: Theme.of(context)
                       .textTheme
                       .bodyMedium!
                       .copyWith(
                     color: Colors.white,
                     fontSize: 18
                   ),
                 ),
               ],
             ),
           )),
     );
  }
}

class _HelpDialog extends StatelessWidget {
  final String? why;
  final String? what;

  const _HelpDialog({Key? key, this.why, this.what}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Container(
        color: Colors.black.withOpacity(0.5),
        child: GestureDetector(
          onTap: () {},
          child: SafeArea(
            top: false,
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (why != null) ...[
                        Text(
                          "Why we ask this?",
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          why!,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF26204A),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      if (what != null) ...[
                        Text(
                          "What it means",
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          what!,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF26204A),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          "If you’re unsure, you can skip for now. \nYou can always update this later.",
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF26204A),
                          ),
                        ),
                      ],
                      const SizedBox(height: 24),
                      SizedBox(
                        width: 250,
                        height: 50,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text(
                            "Continue",
                            style: GoogleFonts.mulish(
                              color: Colors.white,
                              fontSize: 20,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}




