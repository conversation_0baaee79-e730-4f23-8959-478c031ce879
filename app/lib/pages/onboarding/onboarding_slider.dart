import 'package:another_xlider/another_xlider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:another_xlider/models/handler.dart';
import 'package:another_xlider/models/slider_step.dart';
import 'package:another_xlider/models/tooltip/tooltip.dart';
import 'package:another_xlider/models/trackbar.dart';


class CustomIntSlider extends StatefulWidget {
  final int value;
  final int min;
  final int max;
  final Color colour;
  final ValueChanged<int>? onChanged;

  const CustomIntSlider({
    Key? key,
    required this.value,
    required this.min,
    required this.max,
    required this.colour,
    this.onChanged,
  }) : super(key: key);

  @override
  State<CustomIntSlider> createState() => _CustomIntSliderState();
}

class _CustomIntSliderState extends State<CustomIntSlider> {
  late int currentValue;

  @override
  void initState() {
    super.initState();
    currentValue = widget.value;
  }

  void _updateValue(int newValue) {
    setState(() {
      currentValue = newValue;
    });
    widget.onChanged?.call(newValue);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        FlutterSlider(
          values: [currentValue.toDouble()],
          max: widget.max.toDouble(),
          min: widget.min.toDouble(),
          handlerHeight: 45,
          handler: FlutterSliderHandler(
            decoration: const BoxDecoration(),
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(color: widget.colour, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(1, 4),
                  ),
                ],
              ),
              child: Text(
                '$currentValue',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          trackBar: FlutterSliderTrackBar(
            activeTrackBarHeight: 8,
            inactiveTrackBarHeight: 8,
            activeTrackBar: BoxDecoration(
              color: widget.colour,
              borderRadius: BorderRadius.circular(30),
            ),
            inactiveTrackBar: BoxDecoration(
              color: const Color(0xFFD9D9D9),
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          tooltip: FlutterSliderTooltip(disabled: true),
          step: FlutterSliderStep(step: 1),
          onDragging: (_, lower, __) => _updateValue((lower as double).round()),
        ),
      ],
    );
  }
}