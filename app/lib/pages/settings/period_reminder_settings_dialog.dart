import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:account_management/application/period_reminder_settings_bloc/period_reminder_settings_bloc.dart';
import 'package:account_management/domain/model/period_reminder_settings.dart';

class PeriodReminderSettingsDialog extends StatefulWidget {
  final PeriodReminderSettings initialSettings;
  final String reminderType; // 'period' or 'ovulation'

  const PeriodReminderSettingsDialog({
    Key? key,
    required this.initialSettings,
    required this.reminderType,
  }) : super(key: key);

  @override
  State<PeriodReminderSettingsDialog> createState() => _PeriodReminderSettingsDialogState();
}

class _PeriodReminderSettingsDialogState extends State<PeriodReminderSettingsDialog> {
  late bool _isEnabled;
  late int _daysBefore;

  @override
  void initState() {
    super.initState();
    if (widget.reminderType == 'period') {
      _isEnabled = widget.initialSettings.isPeriodReminderEnabled;
      _daysBefore = widget.initialSettings.periodReminderDaysBefore;
    } else {
      _isEnabled = widget.initialSettings.isOvulationReminderEnabled;
      _daysBefore = widget.initialSettings.ovulationReminderDaysBefore;
    }
  }

  String get _title => widget.reminderType == 'period' ? 'Period Reminder' : 'Ovulation Reminder';
  String get _description => widget.reminderType == 'period' 
      ? 'Get notified before your period starts'
      : 'Get notified before your ovulation window';

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xffFAF2DF),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _title,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: const Color(0xff26204a),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Color(0xff26204a)),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: const Color(0xff26204a).withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 24),
            
            // Enable/Disable Switch
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Enable Reminder',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: const Color(0xff26204a),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Switch(
                  value: _isEnabled,
                  onChanged: (value) {
                    setState(() {
                      _isEnabled = value;
                    });
                  },
                  activeColor:   AppTheme.primaryColor,
                ),
              ],
            ),
            
            if (_isEnabled) ...[
              const SizedBox(height: 24),
              Text(
                'Remind me',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: const Color(0xff26204a),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              
              // Days selection
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(5, (index) {
                  final days = index + 1;
                  final isSelected = _daysBefore == days;
                  
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _daysBefore = days;
                      });
                    },
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: isSelected 
                            ?   AppTheme.primaryColor
                            : Colors.white,
                        border: Border.all(
                          color:   AppTheme.primaryColor,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '$days',
                              style: TextStyle(
                                color: isSelected 
                                    ? Colors.white 
                                    :   AppTheme.primaryColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              days == 1 ? 'day' : 'days',
                              style: TextStyle(
                                color: isSelected 
                                    ? Colors.white 
                                    :   AppTheme.primaryColor,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ),
              const SizedBox(height: 8),
              Center(
                child: Text(
                  'before',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xff26204a).withOpacity(0.7),
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 32),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: const Color(0xff26204a).withOpacity(0.7),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // Update the settings and save
                    final updatedSettings = widget.reminderType == 'period'
                        ? widget.initialSettings.copyWith(
                            isPeriodReminderEnabled: _isEnabled,
                            periodReminderDaysBefore: _daysBefore,
                          )
                        : widget.initialSettings.copyWith(
                            isOvulationReminderEnabled: _isEnabled,
                            ovulationReminderDaysBefore: _daysBefore,
                          );
                    
                    Navigator.of(context).pop(updatedSettings);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:   AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Save'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
