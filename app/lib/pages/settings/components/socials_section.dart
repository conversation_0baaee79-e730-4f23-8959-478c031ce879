import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../utils/url_utils.dart';
import 'settings_section.dart';
import 'common_settings_tile.dart';

class SocialsSection extends StatelessWidget {
  const SocialsSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SettingsSection(
      title: 'Socials',
      children: [
        // Instagram
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.instagram,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          title: 'Instagram',
          onTap: () async {
            final success = await UrlUtils.launchInstagram();
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Unable to open Instagram. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),

        // TikTok
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.tiktok,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          title: 'TikTok',
          onTap: () async {
            final success = await UrlUtils.launchTikTok();
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Unable to open TikTok. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),

        // LinkedIn
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.linkedin,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          title: 'LinkedIn',
          onTap: () async {
            final success = await UrlUtils.launchLinkedIn();
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Unable to open LinkedIn. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),

        // More About Juno
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.globe,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          title: 'More About Juno',
          onTap: () async {
            final success = await UrlUtils.launchJunoWebsite();
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Unable to open Juno website. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),

        // Get a Device
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.cartShopping,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          title: 'Get a Device',
          onTap: () async {
            final success = await UrlUtils.launchBuyDevice();
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'Unable to open device purchase form. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),

        // Share the App
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.share,
            size: 24,
            color: AppTheme.primaryColor,
          ),
          title: 'Share the App',
          showDivider: false,
          onTap: () {
            UrlUtils.shareApp();
          },
        ),
      ],
    );
  }
}
