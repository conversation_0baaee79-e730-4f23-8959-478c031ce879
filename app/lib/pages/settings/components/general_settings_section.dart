import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:authentication/authentication.dart';
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart';

import '../../../routing/app_pages.gr.dart';
import '../delete_account_dialog.dart';
import 'settings_section.dart';
import 'common_settings_tile.dart';

class GeneralSettingsSection extends StatefulWidget {
  const GeneralSettingsSection({Key? key}) : super(key: key);

  @override
  State<GeneralSettingsSection> createState() => _GeneralSettingsSectionState();
}

class _GeneralSettingsSectionState extends State<GeneralSettingsSection> {
  List<String> languages = ['English', 'French'];
  String currentLanguage = 'English';

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountWatcherBloc, AccountWatcherState>(
      builder: (context, state) {
        return state.maybeWhen(
          loadSuccess: (accountDetails) {
            return SettingsSection(
              title: 'General Settings',
              children: [
                // Change Password (only for email/password users)
                if (accountDetails.providerData?.first.providerId ==
                    "password") ...[
                  _buildChangePasswordItem(context, accountDetails.userEmail),
                ],

                // Change Language
                // _buildLanguageItem(context),
                // const SizedBox(height: 10),
                // const Divider(thickness: 1, height: 7),
                // const SizedBox(height: 10),

                // Delete Account & Data
                _buildDeleteAccountItem(context),
                const SizedBox(height: 10),

                // Logout
                _buildLogoutItem(context),
              ],
            );
          },
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildChangePasswordItem(BuildContext context, String? userEmail) {
    return SettingsTileStyles.defaultTile(
      icon: const Icon(
        Icons.password,
        size: 24,
        color: AppTheme.primaryColor,
      ),
      title: 'Change Password',
      onTap: () {
        showDialog<void>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Do you want to change your password?',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontSize: 24,
                      color: const Color(0xff26204a),
                      fontWeight: FontWeight.w600,
                    ),
              ),
              content: Text(
                'password change email will be sent your account',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontSize: 18,
                      color: const Color(0xff26204a),
                      fontWeight: FontWeight.w400,
                    ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    debugPrint('confirm:$userEmail');
                    // TODO: Implement forgot password functionality
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Password reset functionality will be implemented soon'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  },
                  child: const Text('Confirm'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildLanguageItem(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: ExpansionTile(
        title: const Align(
          alignment: Alignment(-0.70, 0),
          child: Text(
            'Change Language',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xff26204a),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        leading: const Icon(
          Icons.language,
          size: 30,
          color: AppTheme.primaryColor,
        ),
        trailing: const Icon(
          Icons.keyboard_arrow_down_rounded,
          size: 50,
          color: AppTheme.primaryColor,
        ),
        tilePadding: const EdgeInsets.only(left: 10, right: 4),
        children: [
          Form(
            child: Column(
              children: [
                SizedBox(
                  width: 215,
                  child: RadioListTile(
                    title: const Text(
                      'English',
                      style: TextStyle(
                        fontSize: 18,
                        color: Color(0xff26204a),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    value: languages[0],
                    controlAffinity: ListTileControlAffinity.trailing,
                    groupValue: currentLanguage,
                    onChanged: (value) {
                      setState(() {
                        currentLanguage = value.toString();
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 215,
                  child: RadioListTile(
                    title: const Text(
                      'French',
                      style: TextStyle(
                        fontSize: 18,
                        color: Color(0xff26204a),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    value: languages[1],
                    controlAffinity: ListTileControlAffinity.trailing,
                    groupValue: currentLanguage,
                    onChanged: (value) {
                      setState(() {
                        currentLanguage = value.toString();
                      });
                    },
                  ),
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                  style: const ButtonStyle(
                    minimumSize: WidgetStatePropertyAll(Size(213, 45)),
                    backgroundColor: WidgetStatePropertyAll(Color(0xff584294)),
                    foregroundColor: WidgetStatePropertyAll(Colors.white),
                  ),
                  onPressed: () {},
                  child: const Text(
                    'Confirm Language',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeleteAccountItem(BuildContext context) {
    return SettingsTileStyles.defaultTile(
      icon: const Icon(
        Icons.delete_forever,
        size: 24,
        color: AppTheme.primaryColor,
      ),
      title: 'Delete Account & Data',
      onTap: () {
        showDialog<void>(
          context: context,
          builder: (BuildContext context) {
            return const DeleteAccountDialog();
          },
        );
      },
    );
  }

  Widget _buildLogoutItem(BuildContext context) {
    return SettingsTileStyles.defaultTile(
      icon: const Icon(
        Icons.logout,
        size: 24,
        color: AppTheme.primaryColor,
      ),
      title: 'Logout',
      showDivider: false,
      onTap: () {
        showDialog<void>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Logout',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontSize: 24,
                      color: const Color(0xff26204a),
                      fontWeight: FontWeight.w600,
                    ),
              ),
              content: Text(
                'Are you sure you want to logout?',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontSize: 18,
                      color: const Color(0xff26204a),
                      fontWeight: FontWeight.w400,
                    ),
              ),
              actions: [
                TextButton(
                  onPressed: () async {
                    context.read<AuthBloc>().add(const AuthEvent.signedOut());
                    context.router.replaceAll([const WelcomeRoute()]);
                  },
                  child: const Text('Yes'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('No'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
