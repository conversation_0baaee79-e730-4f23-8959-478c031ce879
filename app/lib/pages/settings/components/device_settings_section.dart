import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/bluetooth.dart';

import '../../../helpers.dart';
import 'package:juno_plus/helpers.dart';

import '../../../routing/app_pages.gr.dart';
import 'settings_section.dart';
import 'common_settings_tile.dart';

class DeviceSettingsSection extends StatelessWidget {
  const DeviceSettingsSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, state) {
        return state.maybeWhen(
          connected: (device) => SettingsSection(
            title: 'Device Settings',
            children: [
              // Device Settings
              _buildDeviceSettingsItem(
                context: context,
                icon: 'assets/settings/device.svg',
                title: 'Device Settings',
                subtitle:
                    'Manage your ${device.platformName.isNotEmpty ? device.platformName : 'Juno'} device',
                onTap: () {
                  context.router.push(const DeviceSettingsRoute());
                },
              ),

              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),

              // Quick Software Update Check
              _buildSoftwareUpdateItem(
                context: context,
                device: device,
              ),
            ],
          ),
          orElse: () => SettingsSection(
            title: 'Device Settings',
            children: [
              _buildDeviceSettingsItem(
                context: context,
                icon: 'assets/settings/device.svg',
                title: 'No Device Connected',
                subtitle: 'Connect your Juno device to access settings',
                onTap: null,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDeviceSettingsItem({
    required BuildContext context,
    required String icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.settings,
                color:  AppTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSoftwareUpdateItem({
    required BuildContext context,
    required BluetoothDevice device,
  }) {
    return BlocProvider(
      create: (context) =>
          getIt<OtaUpdateBloc>()..add(OtaUpdateEvent.checkForUpdates(device)),
      child: BlocBuilder<OtaUpdateBloc, OtaUpdateState>(
        builder: (context, otaState) {
          return otaState.maybeWhen(
            checkingForUpdates: () => _buildQuickUpdateItem(
              context: context,
              title: 'Checking for Updates...',
              subtitle: 'Please wait',
              isLoading: true,
            ),
            updateAvailable: (updateInfo) => _buildQuickUpdateItem(
              context: context,
              title: 'Update Available',
              subtitle: 'Version ${updateInfo.version} - Tap to update',
              hasUpdate: true,
              onTap: () {
                context.router.push(const DeviceSettingsRoute());
              },
            ),
            noUpdatesAvailable: () => _buildQuickUpdateItem(
              context: context,
              title: 'Software Up to Date',
              subtitle: 'Your device has the latest firmware',
            ),
            error: (failure) => _buildQuickUpdateItem(
              context: context,
              title: 'Check for Updates',
              subtitle: 'Tap to check for firmware updates',
              onTap: () {
                context.read<OtaUpdateBloc>().add(
                      OtaUpdateEvent.checkForUpdates(device),
                    );
              },
            ),
            orElse: () => _buildQuickUpdateItem(
              context: context,
              title: 'Check for Updates',
              subtitle: 'Tap to check for firmware updates',
              onTap: () {
                context.read<OtaUpdateBloc>().add(
                      OtaUpdateEvent.checkForUpdates(device),
                    );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuickUpdateItem({
    required BuildContext context,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    bool isLoading = false,
    bool hasUpdate = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        decoration: BoxDecoration(
          color: hasUpdate
              ?   AppTheme.primaryColor.withOpacity(0.05)
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: hasUpdate
              ? Border.all(color:   AppTheme.primaryColor.withOpacity(0.3))
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: hasUpdate
                    ? AppTheme.primaryColor.withOpacity(0.2)
                    : AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: isLoading
                  ? const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color:  AppTheme.primaryColor,
                        ),
                      ),
                    )
                  : Icon(
                      hasUpdate ? Icons.system_update_alt : Icons.system_update,
                      color:   AppTheme.primaryColor,
                      size: 24,
                    ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color:
                          hasUpdate ?   AppTheme.primaryColor : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: hasUpdate
                          ?   AppTheme.primaryColor.withOpacity(0.8)
                          : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null && !isLoading)
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: hasUpdate ? AppTheme.primaryColor : Colors.grey,
              ),
          ],
        ),
      ),
    );
  }
}
