import 'package:account_management/account_management.dart';
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart';
import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart';
import 'package:account_management/domain/model/account_details_model.dart';
import 'package:authentication/authentication.dart';
import 'package:auto_route/auto_route.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'components/health_data_section.dart';
import 'components/reminders_section.dart';

import 'components/help_center_section.dart';
import 'components/socials_section.dart';
import 'components/general_settings_section.dart';

@RoutePage()
class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<AccountWatcherBloc>()
            ..add(const AccountWatcherEvent.watchAllStarted()),
        ),
        BlocProvider(
          create: (context) => getIt<AccountManagementBloc>(),
        ),
        BlocProvider(create: (context) => getIt<AuthBloc>()),
        BlocProvider(create: (context) => getIt<UpdateHealthDataBloc>()),
        BlocProvider(
          create: (context) => getIt<PeriodReminderSettingsBloc>()
            ..add(const PeriodReminderSettingsEvent.loadSettings()),
        ),
      ],
      child: BlocBuilder<AccountWatcherBloc, AccountWatcherState>(
        builder: (context, state) {
          return state.maybeWhen(
            loading: () => Scaffold(
              appBar: CurvedAppBar(
                appBarColor: AppTheme.primaryColor,
                logoColor: const Color(0xffFAF2DF),
                height: .35.sw,
              ),
              body: const Center(
                child: CircularProgressIndicator(
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
            loadSuccess: (accountDetails) {
              return Scaffold(
                extendBodyBehindAppBar: true,
                appBar: CurvedAppBar(
                  appBarColor: AppTheme.primaryColor,
                  logoColor: const Color(0xffFAF2DF),
                  height: .35.sw,
                  subtitle: 'Settings',
                ),
                body: Stack(
                  children: [
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xffFBF0D5), // Light cream
                              Color(0xffF8EEFF), // Light purple
                            ],
                          ),
                        ),
                      ),
                    ),
                    SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            SizedBox(height: .35.sw),

                            // Profile Section
                            _buildProfileSection(context, accountDetails),

                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                const SizedBox(height: 35),

                                // Health Data Section
                                const HealthDataSection(),

                                const SizedBox(height: 35),

                                // Reminders Section
                                const RemindersSection(),

                                const SizedBox(height: 35),


                                // Help Center Section
                                const HelpCenterSection(),

                                const SizedBox(height: 35),

                                // Socials Section
                                const SocialsSection(),

                                const SizedBox(height: 35),

                                // General Settings Section
                                const GeneralSettingsSection(),

                                SizedBox(height: .25.sw),
                              ],
                            ),
                          )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
            loadFailure: (_) => Scaffold(
              appBar: CurvedAppBar(
                appBarColor: AppTheme.primaryColor,
                logoColor: const Color(0xffFAF2DF),
                height: .35.sw,
              ),
              body: const Center(
                child: CircularProgressIndicator(
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
            orElse: () => const SizedBox.shrink(),
          );
        },
      ),
    );
  }

  Widget _buildProfileSection(
      BuildContext context, AccountDetailsModel accountDetails) {
    return Column(
      children: [
        const SizedBox(height: 20),
        GestureDetector(
          onTap: () {
            context.router.push(ProfileRoute());
          },
          child: Stack(
            children: [
           
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  width: 1.sw,
                  decoration: const BoxDecoration(
                    color:                Color.fromARGB(255, 255, 252, 246),

                    boxShadow: [
                      BoxShadow(
                        color: Color(0x40000000),
                        blurRadius: 4.0,
                        offset: Offset(0, 1),
                      ),
                    ],
                    borderRadius: BorderRadius.all(Radius.circular(32)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color:  AppTheme.primaryColor,
                              ),
                              child: accountDetails.photoURL != null
                                  ? ClipOval(
                                      child: CachedNetworkImage(
                                        imageUrl: accountDetails.photoURL!,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) =>
                                            const CircularProgressIndicator(),
                                        errorWidget: (context, url, error) =>
                                            const Icon(
                                          Icons.person,
                                          size: 40,
                                          color: Colors.white,
                                        ),
                                      ),
                                    )
                                  : const Icon(
                                      Icons.person,
                                      size: 40,
                                      color: Colors.white,
                                    ),
                            ),
                            const SizedBox(width: 20),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    accountDetails.userName ?? 'User',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                          fontSize: 20,
                                          color: const Color(0xff26204a),
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                  // const SizedBox(height: 5),
                                  // Text(
                                  //   accountDetails.userEmail ?? '',
                                  //   style:
                                  //       Theme.of(context).textTheme.bodySmall!.copyWith(
                                  //             fontSize: 16,
                                  //             color: const Color(0xff26204a),
                                  //             fontWeight: FontWeight.w300,
                                  //           ),
                                  // ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
              ),
              // Edit Profile Button
              Positioned(
                right: 0,
                top: 0,
                child: GestureDetector(
                  onTap: () {
                    context.router.push(ProfileRoute());
                  },
                  child: Container(
                    width: 60.sp,
                    height: 60.sp,
                    decoration: BoxDecoration(
                      color:   AppTheme.primaryColor,
                      shape: BoxShape.circle
                    ),
                    child: Icon(
                      Icons.edit,
                      color: Colors.white,
                      size: 30.sp,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
