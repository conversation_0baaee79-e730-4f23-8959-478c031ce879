import 'dart:async';
import 'package:account_management/application/account_management_bloc/account_management_bloc.dart';
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart';
import 'package:account_management/domain/model/account_details_model.dart';
import 'package:auto_route/auto_route.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../custom_widgets/text_form_feild.dart';
import '../../helpers.dart';
import '../../routing/app_pages.gr.dart';
@RoutePage()
class ProfilePage extends StatelessWidget {
  ProfilePage({super.key});

  final _formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController dobController = TextEditingController();

  Future<DateTime?> dobCalendar(DateTime? dob, BuildContext context) async {
    Completer<DateTime?> completer = Completer<DateTime?>();
    DateTime? selectedDate = dob ?? DateTime.now();

    await AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: .7.sw,
            height: 250,
            child: DatePicker(

              initialDate: dob ?? DateTime.now(),
              minDate: DateTime(1960, 10, 10),
              maxDate: DateTime.now(),
              padding: EdgeInsets.zero,
              currentDate: DateTime.now(),
              selectedDate: selectedDate,
              onDateSelected: (date) {
                selectedDate = date;
              },
              currentDateDecoration: const BoxDecoration(),
              currentDateTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              daysOfTheWeekTextStyle: const TextStyle(
                color: Color(0xff71456F),
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
              disabledCellsDecoration: const BoxDecoration(),
              disabledCellsTextStyle: const TextStyle(),
              enabledCellsDecoration: const BoxDecoration(),
              enabledCellsTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              initialPickerType: PickerType.days,
              selectedCellDecoration: BoxDecoration(
                color: Theme
                    .of(context)
                    .primaryColor,
                shape: BoxShape.circle,
              ),
              selectedCellTextStyle: GoogleFonts.roboto(
                color: const Color(0xffFBF0D5),
                fontWeight: FontWeight.w400,
                fontSize: 12,
              ),
              leadingDateTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              slidersColor: const Color(0xff71456F),
              highlightColor: const Color(0xff71456F),
              slidersSize: 20,
              splashColor: Theme
                  .of(context)
                  .primaryColor
                  .withOpacity(0.5),
              splashRadius: 0,
              centerLeadingDate: true,
            ),
          ),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                completer.complete(selectedDate);
              },
              child: const Text("Confirm"),
            ),
          ),
        ],
      ),
    ).show();
    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider(
              create: (context) =>
              getIt<AccountWatcherBloc>()
                ..add(AccountWatcherEvent.watchAllStarted())),
          BlocProvider<AccountManagementBloc>(
            create: (context) => getIt<AccountManagementBloc>(),
          ),
        ],
        child: MultiBlocListener(
            listeners: [
              BlocListener<AccountManagementBloc, AccountManagementState>(
                listener: (context, state) {
                  state.map(
                    initial: (_) {},
                    updating: (_) {},
                    updated: (_) {

                        Fluttertoast.showToast( msg: 'Account details updated successfully');

                    },
                    updateFailure: (state) {
                      Fluttertoast.showToast( msg: 'update failed');
                    },
                  );
                },
              ),
            ],
            child: BlocBuilder<AccountWatcherBloc, AccountWatcherState>(
                builder: (context, state) {
                  return state.map(
                      initial: (_) => Container(),
                      loading: (_) =>
                          Center(
                            child: CircularProgressIndicator(
                              color: AppTheme.primaryColor,
                            ),
                          ),
                      loadSuccess: (state) {
                        nameController.text = state.accountDetails.userName!;
                        var dob = state.accountDetails.dateOfBirth
                            ?.toDate() ?? DateTime.now();
                        dobController.text =
                        state.accountDetails.dateOfBirth != null ? '${state
                            .accountDetails.dateOfBirth
                            ?.toDate()
                            .day}-${state.accountDetails.dateOfBirth
                            ?.toDate()
                            .month}-${state.accountDetails.dateOfBirth
                            ?.toDate()
                            .year}' : '';
                        return Scaffold(
                          backgroundColor: Color(0xffF8EEFF),
                          extendBodyBehindAppBar: true,
                          appBar: CurvedAppBar(
                          appBarColor: AppTheme.primaryColor,
                          logoColor: Color(0xffFAF2DF),
                          height: .35.sw,
                          topLeftIcon: IconButton(
                            key: const Key('back_button'),
                            icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
                            onPressed: () {
                              context.router.back();
                            },
                          ),

                        ),
                          bottomNavigationBar: Padding(
                            padding: const EdgeInsets.all(25.0),
                            child: ElevatedButton(
                              style: ButtonStyle(
                                minimumSize:
                                WidgetStatePropertyAll(
                                    Size(213, 45)),
                                backgroundColor:
                                WidgetStatePropertyAll(
                                    Color(0xff584294)),
                                foregroundColor:
                                WidgetStatePropertyAll(
                                    Colors.white),
                              ),
                              onPressed: () {
                                if (_formKey.currentState!.validate()) {
                                  context.read<AccountManagementBloc>().add(
                                      AccountManagementEvent.updateAccountDetails(
                                          AccountDetailsModel(
                                              photoURL: state.accountDetails.photoURL,
                                              userName: nameController.text,
                                              dateOfBirth: Timestamp.fromDate(
                                                  dob,
                                              ))));
                                }
                              },
                              child: Text(
                                'Save',
                                style: Theme
                                    .of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                    color: Colors.white, fontSize: 18,fontWeight: FontWeight.w500),
                              ),
                            ),
                          ),
                          body: Container(

                              width: 1.sw,
                              height: 1.sh,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Color(0xffFBF0D5), // Light cream
                                    Color(0xffF8EEFF), // Light purple
                                  ],
                                ),
                              ),

                            child: Column(
                              children: [
                                SizedBox(height: 0.35.sw),
                                SingleChildScrollView(
                                  child: Padding(
                                    padding: const EdgeInsets.all(25.0),
                                    child: Column(
                                      children: [
                                        GestureDetector(
                                          onTap: () {
                                            context.router.push(ProfilePictureRoute());
                                          },
                                          child: Container(
                                            width: 110,
                                            height: 110,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Colors.white,
                                              border: Border.all(
                                                color: AppTheme.primaryColor,
                                                width: 2,
                                              ),
                                            ),
                                            child:  Padding(
                                              padding: const EdgeInsets.all(5.0),
                                              child: Container(
                                                width: 100,
                                                height: 100,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  image: DecorationImage(
                                                    image: CachedNetworkImageProvider(
                                                        state.accountDetails.photoURL ??
                                                            'https://firebasestorage.googleapis.com/v0/b/junoplus-dev.appspot.com/o/profile_place_holder.png?alt=media&token=61113f40-e3b2-4c66-9843-2318aa4f38e0'),
                                                    fit:  state.accountDetails.photoURL !=''? BoxFit.cover:BoxFit.fill,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        GestureDetector(
                                          key: const Key('edit_photo'),
                                          onTap: () async{
                                           context.router.push(ProfilePictureRoute());
                                          },
                                          child: Text(
                                            'Edit photo',
                                            style: Theme
                                                .of(context)
                                                .textTheme
                                                .bodyMedium!
                                                .copyWith(
                                                color: Theme.of(context).primaryColor, fontSize: 16,fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                        Form(
                                          key: _formKey,
                                          child: Column(
                                            children: [
                                              MyTextFormField(
                                                heading: 'Name',
                                                refkey: const Key('name'),
                                                inputStyle: Theme
                                                    .of(context)
                                                    .textTheme
                                                    .bodyMedium!
                                                    .copyWith(fontSize: 16,
                                                    color: Colors.black),
                                                isEmail: true,
                                                controller: nameController,
                                                onchanged: (val) {},
                                                validator: (val) {
                                                  if (val!.length > 20) return 'Name too long';
                                                  return null;
                                                },
                                              ),
                                              GestureDetector(
                                                onTap: () async {
                                                  DateTime? dateTime = await dobCalendar(
                                                      dob, context);
                                                  if (dateTime != null) {
                                                    dob = dateTime;
                                                    dobController.text =
                                                    '${dateTime.day}-${dateTime
                                                        .month}-${dateTime.year}';
                                                    // context.read.add(
                                                    // UpdateAccountDetails(AccountDetailsModel(
                                                    // userName: nameController.text,
                                                    // dateOfBirth: Timestamp.fromDate(dateTime))),
                                                    // );
                                                  }
                                                },
                                                child: MyTextFormField(
                                                  heading: 'Date of Birth',
                                                  controller: dobController,
                                                  inputStyle: Theme
                                                      .of(context)
                                                      .textTheme
                                                      .bodyMedium!
                                                      .copyWith(
                                                      fontSize: 16, color: Colors.black),
                                                  refkey: const Key('dob'),
                                                  isEnabled: false,
                                                  isEmail: true,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }, loadFailure: (_) =>
                      Center(
                        child: CircularProgressIndicator(
                          color: AppTheme.primaryColor,
                        ),
                      ));
                },

            )));
  }
}
