import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';

class DeleteAccountDialog extends StatelessWidget {
  const DeleteAccountDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Delete Account & Data',
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontSize: 24,
              color: const Color(0xff26204a),
              fontWeight: FontWeight.w600,
            ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'This action will permanently delete:',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 18,
                  color: const Color(0xff26204a),
                  fontWeight: FontWeight.w500,
                ),
          ),
          const SizedBox(height: 12),
          _buildBulletPoint(context, 'Your account and profile information'),
          _buildBulletPoint(context, 'All therapy session data'),
          _buildBulletPoint(context, 'Period tracking history'),
          _buildBulletPoint(context, 'Device connection settings'),
          _buildBulletPoint(context, 'All app preferences and settings'),
          const SizedBox(height: 16),
          Text(
            'This action cannot be undone. Are you sure you want to continue?',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 16,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text(
            'Cancel',
            style: TextStyle(
              color:   AppTheme.primaryColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            // TODO: Implement account deletion logic
            // This should call the appropriate bloc/service to delete the account
            // For now, we'll just show a toast message
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content:
                    Text('Account deletion feature will be implemented soon'),
                backgroundColor: Colors.orange,
              ),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            'Delete Account',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '• ',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xff26204a),
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: 16,
                    color: const Color(0xff26204a),
                    fontWeight: FontWeight.w400,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
