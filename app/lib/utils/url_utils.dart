import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/material.dart';

class UrlUtils {
  // Social media URLs
  static const String instagramUrl = 'https://www.instagram.com/junotechno/';
  static const String tiktokUrl = 'https://www.tiktok.com/@junotechnologies';
  static const String linkedinUrl =
      'https://www.linkedin.com/company/juno-technologies/posts/?feedView=all';
  static const String junoWebsiteUrl = 'https://junotechno.com';
  static const String contactUsUrl = 'https://junotechno.com/contact-us/';
  static const String buyDeviceUrl =
      'https://djr7a623vt1.typeform.com/to/hqwGMsiL?typeform-source=junotechno.com';

  // App sharing text
  static const String appShareText =
      'Check out the Juno+ app for managing your Juno device! Download it now.';

  /// Launch a URL in the default browser
  static Future<bool> launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);

      // Try to launch the URL directly
      final bool launched = await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        print('Failed to launch URL: $url');
        return false;
      }

      return true;
    } catch (e) {
      print('Error launching URL: $e');
      return false;
    }
  }

  /// Share the app
  static Future<void> shareApp() async {
    try {
      await Share.share(appShareText);
    } catch (e) {
      print('Error sharing app: $e');
    }
  }

  /// Launch Instagram
  static Future<bool> launchInstagram() async {
    return await launchURL(instagramUrl);
  }

  /// Launch TikTok
  static Future<bool> launchTikTok() async {
    return await launchURL(tiktokUrl);
  }

  /// Launch LinkedIn
  static Future<bool> launchLinkedIn() async {
    return await launchURL(linkedinUrl);
  }

  /// Launch Juno website
  static Future<bool> launchJunoWebsite() async {
    return await launchURL(junoWebsiteUrl);
  }

  /// Launch Contact Us page
  static Future<bool> launchContactUs() async {
    return await launchURL(contactUsUrl);
  }

  /// Launch Buy Device form
  static Future<bool> launchBuyDevice() async {
    return await launchURL(buyDeviceUrl);
  }
}
