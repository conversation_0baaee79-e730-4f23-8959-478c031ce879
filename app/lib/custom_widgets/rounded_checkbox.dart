import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RoundedCheckbox extends StatelessWidget {
  final bool isChecked;
  final VoidCallback? onTap;
  final double size;
  final Color checkedColor;
  final Color uncheckedColor;
  final Color checkmarkColor;
  final bool isEnabled;

  const RoundedCheckbox({
    super.key,
    required this.isChecked,
    this.onTap,
    this.size = 24.0,
    this.checkedColor = const Color(0xFFE91E63),
    this.uncheckedColor = const Color(0xFFE0E0E0),
    this.checkmarkColor = Colors.white,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: size.w,
        height: size.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isChecked ? checkedColor : uncheckedColor,
          border: Border.all(
            color: isEnabled 
                ? (isChecked ? checkedColor : const Color(0xFFBDBDBD))
                : const Color(0xFFE0E0E0),
            width: 2.0,
          ),
        ),
        child: isChecked
            ? Icon(
                Icons.check,
                size: (size * 0.6).sp,
                color: checkmarkColor,
              )
            : null,
      ),
    );
  }
}
