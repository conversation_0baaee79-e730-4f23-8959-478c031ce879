import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:design_system/design/theme.dart';

class WeeklyCalendar extends StatefulWidget {
  final DateTime selectedDate;
  final void Function(DateTime) onDateSelected;
  final Set<DateTime> periodDates;
  final Set<DateTime> ovulationDates;

  const WeeklyCalendar({
    required this.selectedDate,
    required this.onDateSelected,
    this.periodDates = const {},
    this.ovulationDates = const {},
    Key? key,
  }) : super(key: key);

  @override
  State<WeeklyCalendar> createState() => _WeeklyCalendarState();
}

class _WeeklyCalendarState extends State<WeeklyCalendar> {
  late DateTime _currentWeek;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentWeek = _getWeekStart(widget.selectedDate);
    _pageController = PageController(
        initialPage: 1000); // Start in middle for infinite scroll
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  DateTime _getWeekStart(DateTime date) {
    // Get the start of the week (Sunday)
    final weekday = date.weekday % 7; // Sunday = 0
    return DateTime(date.year, date.month, date.day - weekday);
  }

  List<DateTime> _getWeekDates(DateTime weekStart) {
    return List.generate(7, (index) => weekStart.add(Duration(days: index)));
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  bool _isPeriodDate(DateTime date) {
    return widget.periodDates.any((periodDate) => _isSameDay(date, periodDate));
  }

  bool _isOvulationDate(DateTime date) {
    return widget.ovulationDates
        .any((ovulationDate) => _isSameDay(date, ovulationDate));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Month/Year header with navigation
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    _currentWeek = _currentWeek.subtract(Duration(days: 7));
                  });
                },
                icon: Icon(Icons.chevron_left, color: AppTheme.primaryColor),
              ),
              Text(
                DateFormat('MMMM yyyy').format(_currentWeek),
                style: GoogleFonts.mulish(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _currentWeek = _currentWeek.add(Duration(days: 7));
                  });
                },
                icon: Icon(Icons.chevron_right, color: AppTheme.primaryColor),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Combined day and date with pill shape
          Row(
            children: _getWeekDates(_currentWeek).asMap().entries.map((entry) {
              final index = entry.key;
              final date = entry.value;
              final dayLetter = ['S', 'M', 'T', 'W', 'T', 'F', 'S'][index];

              final isSelected = _isSameDay(date, widget.selectedDate);
              final isPeriod = _isPeriodDate(date);
              final isOvulation = _isOvulationDate(date);
              final isToday = _isSameDay(date, DateTime.now());
              final isFuture = date.isAfter(DateTime.now());

              return Expanded(
                child: GestureDetector(
                    onTap: isFuture ? null : () => widget.onDateSelected(date),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      child: Container(
                        height: 120.h,
                        width: 90.w,
                        margin: EdgeInsets.symmetric(horizontal: 2.w),
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 12.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30),
                          color: isFuture
                              ? Colors.grey.withValues(alpha: 0.2)
                              : isSelected
                                  ? AppTheme.primaryColor
                                  : isPeriod
                                      ? AppTheme.primaryColor
                                          .withValues(alpha: 0.2)
                                      : isOvulation
                                          ? Color(0xffECA83D)
                                              .withValues(alpha: 0.2)
                                          : AppTheme.primaryColor
                                              .withValues(alpha: 0.1),
                          border: isToday && !isSelected
                              ? Border.all(
                                  color: AppTheme.primaryColor, width: 2)
                              : null,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Day letter
                            Text(
                              dayLetter,
                              style: GoogleFonts.mulish(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.w500,
                                color: isFuture
                                    ? Colors.grey
                                    : isSelected
                                        ? Colors.white
                                        : AppTheme.primaryColor,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            // Date number
                            Container(
                                height: 60.h,
                                width: 60.w,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: isFuture
                                      ? Colors.grey.withValues(alpha: 0.3)
                                      : isSelected
                                          ? AppTheme.primaryColor
                                          : isPeriod
                                              ? AppTheme.primaryColor
                                              : isOvulation
                                                  ? Color(0xffECA83D)
                                                  : AppTheme.primaryColor
                                                      .withValues(alpha: 0.1),
                                ),
                                child: Center(
                                    child: Padding(
                                  padding: EdgeInsets.all(5.w),
                                  child: Text(
                                    '${date.day}',
                                    style: GoogleFonts.mulish(
                                      fontSize: 25.sp,
                                      fontWeight: FontWeight.w600,
                                      color: isFuture
                                          ? Colors.grey
                                          : isSelected
                                              ? Colors.white
                                              : isPeriod
                                                  ? Colors.white
                                                  : isOvulation
                                                      ? AppTheme.primaryColor
                                                      : AppTheme.primaryColor,
                                    ),
                                  ),
                                )))
                          ],
                        ),
                      ),
                    )),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
