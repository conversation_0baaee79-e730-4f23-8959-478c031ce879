// import 'package:flutter/material.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:mockito/mockito.dart';
// import 'package:mockito/annotations.dart';
// import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
// import 'package:account_management/application/manage_period_tracking_bloc/manage_period_tracking_bloc.dart';
// import 'package:juno_plus/pages/dashboard/period_tracking_view_page.dart';
// import 'package:juno_plus/pages/dashboard/period_tracking_insights.dart';
//
// // Generate mocks
// @GenerateMocks([PeriodTrackingWatcherBloc, ManagePeriodTrackingBloc])
// import 'period_tracking_performance_test.mocks.dart';
//
// void main() {
//   group('Period Tracking Performance Tests', () {
//     late MockPeriodTrackingWatcherBloc mockWatcherBloc;
//     late MockManagePeriodTrackingBloc mockManageBloc;
//
//     setUp(() {
//       mockWatcherBloc = MockPeriodTrackingWatcherBloc();
//       mockManageBloc = MockManagePeriodTrackingBloc();
//
//       // Setup default state
//       when(mockWatcherBloc.state).thenReturn(
//         PeriodTrackingWatcherState.data(
//           periodTrackingDetails: [],
//           focusedDay: DateTime.now(),
//           selectedDays: {
//             DateTime(2024, 1, 15),
//             DateTime(2024, 1, 16),
//             DateTime(2024, 1, 17),
//           },
//           ovulationDays: {
//             DateTime(2024, 1, 28),
//             DateTime(2024, 1, 29),
//           },
//           selectedPeriodTrackingDay: const PeriodTrackingModel.empty(),
//           updateCounter: 0,
//         ),
//       );
//
//       when(mockWatcherBloc.stream).thenAnswer((_) => Stream.value(
//         PeriodTrackingWatcherState.data(
//           periodTrackingDetails: [],
//           focusedDay: DateTime.now(),
//           selectedDays: {
//             DateTime(2024, 1, 15),
//             DateTime(2024, 1, 16),
//             DateTime(2024, 1, 17),
//           },
//           ovulationDays: {
//             DateTime(2024, 1, 28),
//             DateTime(2024, 1, 29),
//           },
//           selectedPeriodTrackingDay: const PeriodTrackingModel.empty(),
//           updateCounter: 0,
//         ),
//       ));
//
//       when(mockManageBloc.state).thenReturn(
//         const ManagePeriodTrackingState.initial(),
//       );
//
//       when(mockManageBloc.stream).thenAnswer((_) => Stream.value(
//         const ManagePeriodTrackingState.initial(),
//       ));
//     });
//
//     testWidgets('View page should render faster than edit page', (WidgetTester tester) async {
//       // Test view page performance
//       final viewPageStopwatch = Stopwatch()..start();
//
//       await tester.pumpWidget(
//         MaterialApp(
//           home: BlocProvider<PeriodTrackingWatcherBloc>.value(
//             value: mockWatcherBloc,
//             child: const PeriodTrackingViewScaffold(),
//           ),
//         ),
//       );
//
//       await tester.pumpAndSettle();
//       viewPageStopwatch.stop();
//
//       // Test edit page performance
//       final editPageStopwatch = Stopwatch()..start();
//
//       await tester.pumpWidget(
//         MaterialApp(
//           home: MultiBlocProvider(
//             providers: [
//               BlocProvider<PeriodTrackingWatcherBloc>.value(value: mockWatcherBloc),
//               BlocProvider<ManagePeriodTrackingBloc>.value(value: mockManageBloc),
//             ],
//             child: const PeriodTrackingEditScaffold(),
//           ),
//         ),
//       );
//
//       await tester.pumpAndSettle();
//       editPageStopwatch.stop();
//
//       // View page should be faster or at least not significantly slower
//       print('View page render time: ${viewPageStopwatch.elapsedMilliseconds}ms');
//       print('Edit page render time: ${editPageStopwatch.elapsedMilliseconds}ms');
//
//       // Allow some tolerance, but view page should generally be faster
//       expect(viewPageStopwatch.elapsedMilliseconds, lessThanOrEqualTo(editPageStopwatch.elapsedMilliseconds + 100));
//     });
//
//     testWidgets('View page should display period and ovulation dates correctly', (WidgetTester tester) async {
//       await tester.pumpWidget(
//         MaterialApp(
//           home: BlocProvider<PeriodTrackingWatcherBloc>.value(
//             value: mockWatcherBloc,
//             child: const PeriodTrackingViewScaffold(),
//           ),
//         ),
//       );
//
//       await tester.pumpAndSettle();
//
//       // Check that the page renders without errors
//       expect(find.text('Period Insights'), findsOneWidget);
//       expect(find.byIcon(Icons.edit), findsOneWidget);
//
//       // Check that calendar months are displayed
//       expect(find.byType(ListView), findsOneWidget);
//     });
//
//     testWidgets('Edit page should have edit functionality', (WidgetTester tester) async {
//       await tester.pumpWidget(
//         MaterialApp(
//           home: MultiBlocProvider(
//             providers: [
//               BlocProvider<PeriodTrackingWatcherBloc>.value(value: mockWatcherBloc),
//               BlocProvider<ManagePeriodTrackingBloc>.value(value: mockManageBloc),
//             ],
//             child: const PeriodTrackingEditScaffold(),
//           ),
//         ),
//       );
//
//       await tester.pumpAndSettle();
//
//       // Check that the edit page renders
//       expect(find.text('Edit Period Insights'), findsOneWidget);
//       expect(find.byIcon(Icons.visibility), findsOneWidget);
//       expect(find.text('Edit'), findsOneWidget);
//     });
//   });
// }
