import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:bluetooth/infrastructure/firmware_manager.dart';

void main() {
  group('Firmware Asset Tests', () {
    test('should load firmware from app assets', () async {
      try {
        print('🔍 Testing firmware asset loading from app...');
        
        // This should now load from app/assets/firmware/dfu_application.zip
        final firmwareData = await FirmwareManager.loadFirmwarePackage();
        
        expect(firmwareData, isNotNull);
        expect(firmwareData.isNotEmpty, true);
        expect(firmwareData.length, greaterThan(100000)); // Should be ~173KB
        
        print('✅ Firmware loaded from app assets successfully');
        print('📦 Package size: ${firmwareData.length} bytes');
        
        // Verify it's a valid ZIP file
        if (firmwareData.length >= 4) {
          final header = firmwareData.sublist(0, 4);
          final isZip = header[0] == 0x50 && header[1] == 0x4B; // PK header
          print('📋 Is valid ZIP file: $isZip');
          expect(isZip, true, reason: 'Firmware should be a valid ZIP file');
        }
        
        // Test version detection
        final version = await FirmwareManager.getLatestFirmwareVersion();
        print('🔍 Latest firmware version: $version');
        expect(version, isNotEmpty);
        expect(version, isNot('Unknown'));
        
      } catch (e) {
        print('❌ Failed to load firmware from app assets: $e');
        fail('Should be able to load firmware from app assets: $e');
      }
    });
    
    test('should calculate correct firmware size', () async {
      try {
        final size = await FirmwareManager.getFirmwareSize();
        
        expect(size, greaterThan(100000)); // Should be ~173KB
        expect(size, lessThan(500000)); // Reasonable upper bound
        
        print('✅ Firmware size: $size bytes (${(size / 1024).toStringAsFixed(1)} KB)');
      } catch (e) {
        print('❌ Failed to calculate firmware size: $e');
        fail('Should be able to calculate firmware size: $e');
      }
    });
  });
}
