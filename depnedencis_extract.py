import csv
import requests
from datetime import datetime


# Function to fetch details from pub.dev for a specific version
def fetch_package_details(package_name, version):
    try:
        response = requests.get(f'https://pub.dev/api/packages/{package_name}/versions/{version}')
        response.raise_for_status()
        data = response.json()

        # Extract release date and other details for the specific version
        release_date_raw = data['published']
        release_date = datetime.strptime(release_date_raw, '%Y-%m-%dT%H:%M:%S.%fZ').strftime(
            '%d/%m/%Y')  # Format date as dd/mm/yyyy
        documentation = f'https://pub.dev/packages/{package_name}/versions/{version}'  # Hyperlink for documentation
        purpose = data['pubspec'].get('description', 'N/A')
        category = ', '.join(data['pubspec'].get('topics', []))  # Using topics as a category
        operating_requirement = data['pubspec'].get('environment', 'N/A')  # Extract environment details

        return release_date, documentation, purpose, category, operating_requirement
    except requests.RequestException as e:
       debugPrint(f'Request failed: {e}')
        return 'N/A', 'N/A', 'N/A', 'N/A', 'N/A'


# Read the packages and versions from a CSV file
input_csv_filename = 'dependencies.csv'
packages = []

with open(input_csv_filename, mode='r', encoding='utf-8') as file:
    reader = csv.reader(file)
    next(reader)  # Skip header row
    for row in reader:
        if row:
            packages.append(
                (row[0], row[1]))  # Assuming the package name is in the first column and version in the second column

# Fetch details for each package and write to output CSV file
output_csv_filename = 'all_package_details.csv'

with open(output_csv_filename, mode='w', newline='', encoding='utf-8') as file:
    writer = csv.writer(file)
    writer.writerow(['Category', 'Tool (Name)', 'Release Version', 'Release Date', 'Documentation (Web link)',
                     'Purpose (Requirement)', 'Operating Requirement', 'Where to find SOUP Bug/Release Report'])

    for package_name, version in packages:
        release_date, documentation, purpose, category, operating_requirement = fetch_package_details(package_name,
                                                                                                      version)
        writer.writerow([category, package_name, version, release_date, documentation, purpose, operating_requirement,
                         documentation])

print(f'Details for all packages have been written to {output_csv_filename}')
