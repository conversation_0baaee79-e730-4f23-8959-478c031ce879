// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bluetooth_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BluetoothFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BluetoothFailureCopyWith<$Res> {
  factory $BluetoothFailureCopyWith(
          BluetoothFailure value, $Res Function(BluetoothFailure) then) =
      _$BluetoothFailureCopyWithImpl<$Res, BluetoothFailure>;
}

/// @nodoc
class _$BluetoothFailureCopyWithImpl<$Res, $Val extends BluetoothFailure>
    implements $BluetoothFailureCopyWith<$Res> {
  _$BluetoothFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$BluetoothDisabledImplCopyWith<$Res> {
  factory _$$BluetoothDisabledImplCopyWith(_$BluetoothDisabledImpl value,
          $Res Function(_$BluetoothDisabledImpl) then) =
      __$$BluetoothDisabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BluetoothDisabledImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$BluetoothDisabledImpl>
    implements _$$BluetoothDisabledImplCopyWith<$Res> {
  __$$BluetoothDisabledImplCopyWithImpl(_$BluetoothDisabledImpl _value,
      $Res Function(_$BluetoothDisabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BluetoothDisabledImpl implements BluetoothDisabled {
  const _$BluetoothDisabledImpl();

  @override
  String toString() {
    return 'BluetoothFailure.bluetoothDisabled()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BluetoothDisabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return bluetoothDisabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return bluetoothDisabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (bluetoothDisabled != null) {
      return bluetoothDisabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return bluetoothDisabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return bluetoothDisabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (bluetoothDisabled != null) {
      return bluetoothDisabled(this);
    }
    return orElse();
  }
}

abstract class BluetoothDisabled implements BluetoothFailure {
  const factory BluetoothDisabled() = _$BluetoothDisabledImpl;
}

/// @nodoc
abstract class _$$DeviceConnectionFailedImplCopyWith<$Res> {
  factory _$$DeviceConnectionFailedImplCopyWith(
          _$DeviceConnectionFailedImpl value,
          $Res Function(_$DeviceConnectionFailedImpl) then) =
      __$$DeviceConnectionFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceConnectionFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$DeviceConnectionFailedImpl>
    implements _$$DeviceConnectionFailedImplCopyWith<$Res> {
  __$$DeviceConnectionFailedImplCopyWithImpl(
      _$DeviceConnectionFailedImpl _value,
      $Res Function(_$DeviceConnectionFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeviceConnectionFailedImpl implements DeviceConnectionFailed {
  const _$DeviceConnectionFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.deviceConnectionFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceConnectionFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return deviceConnectionFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return deviceConnectionFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (deviceConnectionFailed != null) {
      return deviceConnectionFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return deviceConnectionFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return deviceConnectionFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (deviceConnectionFailed != null) {
      return deviceConnectionFailed(this);
    }
    return orElse();
  }
}

abstract class DeviceConnectionFailed implements BluetoothFailure {
  const factory DeviceConnectionFailed() = _$DeviceConnectionFailedImpl;
}

/// @nodoc
abstract class _$$NoDevicesFoundImplCopyWith<$Res> {
  factory _$$NoDevicesFoundImplCopyWith(_$NoDevicesFoundImpl value,
          $Res Function(_$NoDevicesFoundImpl) then) =
      __$$NoDevicesFoundImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoDevicesFoundImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$NoDevicesFoundImpl>
    implements _$$NoDevicesFoundImplCopyWith<$Res> {
  __$$NoDevicesFoundImplCopyWithImpl(
      _$NoDevicesFoundImpl _value, $Res Function(_$NoDevicesFoundImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NoDevicesFoundImpl implements NoDevicesFound {
  const _$NoDevicesFoundImpl();

  @override
  String toString() {
    return 'BluetoothFailure.noDevicesFound()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NoDevicesFoundImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return noDevicesFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return noDevicesFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (noDevicesFound != null) {
      return noDevicesFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return noDevicesFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return noDevicesFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (noDevicesFound != null) {
      return noDevicesFound(this);
    }
    return orElse();
  }
}

abstract class NoDevicesFound implements BluetoothFailure {
  const factory NoDevicesFound() = _$NoDevicesFoundImpl;
}

/// @nodoc
abstract class _$$SearchTimeoutImplCopyWith<$Res> {
  factory _$$SearchTimeoutImplCopyWith(
          _$SearchTimeoutImpl value, $Res Function(_$SearchTimeoutImpl) then) =
      __$$SearchTimeoutImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SearchTimeoutImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$SearchTimeoutImpl>
    implements _$$SearchTimeoutImplCopyWith<$Res> {
  __$$SearchTimeoutImplCopyWithImpl(
      _$SearchTimeoutImpl _value, $Res Function(_$SearchTimeoutImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SearchTimeoutImpl implements SearchTimeout {
  const _$SearchTimeoutImpl();

  @override
  String toString() {
    return 'BluetoothFailure.searchTimeout()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SearchTimeoutImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return searchTimeout();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return searchTimeout?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (searchTimeout != null) {
      return searchTimeout();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return searchTimeout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return searchTimeout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (searchTimeout != null) {
      return searchTimeout(this);
    }
    return orElse();
  }
}

abstract class SearchTimeout implements BluetoothFailure {
  const factory SearchTimeout() = _$SearchTimeoutImpl;
}

/// @nodoc
abstract class _$$PermissionDeniedImplCopyWith<$Res> {
  factory _$$PermissionDeniedImplCopyWith(_$PermissionDeniedImpl value,
          $Res Function(_$PermissionDeniedImpl) then) =
      __$$PermissionDeniedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PermissionDeniedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$PermissionDeniedImpl>
    implements _$$PermissionDeniedImplCopyWith<$Res> {
  __$$PermissionDeniedImplCopyWithImpl(_$PermissionDeniedImpl _value,
      $Res Function(_$PermissionDeniedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PermissionDeniedImpl implements PermissionDenied {
  const _$PermissionDeniedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.permissionDenied()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PermissionDeniedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return permissionDenied();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return permissionDenied?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (permissionDenied != null) {
      return permissionDenied();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return permissionDenied(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return permissionDenied?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (permissionDenied != null) {
      return permissionDenied(this);
    }
    return orElse();
  }
}

abstract class PermissionDenied implements BluetoothFailure {
  const factory PermissionDenied() = _$PermissionDeniedImpl;
}

/// @nodoc
abstract class _$$UnexpectedImplCopyWith<$Res> {
  factory _$$UnexpectedImplCopyWith(
          _$UnexpectedImpl value, $Res Function(_$UnexpectedImpl) then) =
      __$$UnexpectedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnexpectedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$UnexpectedImpl>
    implements _$$UnexpectedImplCopyWith<$Res> {
  __$$UnexpectedImplCopyWithImpl(
      _$UnexpectedImpl _value, $Res Function(_$UnexpectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnexpectedImpl implements Unexpected {
  const _$UnexpectedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.unexpected()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class Unexpected implements BluetoothFailure {
  const factory Unexpected() = _$UnexpectedImpl;
}

/// @nodoc
abstract class _$$SaveDeviceFailedImplCopyWith<$Res> {
  factory _$$SaveDeviceFailedImplCopyWith(_$SaveDeviceFailedImpl value,
          $Res Function(_$SaveDeviceFailedImpl) then) =
      __$$SaveDeviceFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SaveDeviceFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$SaveDeviceFailedImpl>
    implements _$$SaveDeviceFailedImplCopyWith<$Res> {
  __$$SaveDeviceFailedImplCopyWithImpl(_$SaveDeviceFailedImpl _value,
      $Res Function(_$SaveDeviceFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SaveDeviceFailedImpl implements SaveDeviceFailed {
  const _$SaveDeviceFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.saveDeviceFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SaveDeviceFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return saveDeviceFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return saveDeviceFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (saveDeviceFailed != null) {
      return saveDeviceFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return saveDeviceFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return saveDeviceFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (saveDeviceFailed != null) {
      return saveDeviceFailed(this);
    }
    return orElse();
  }
}

abstract class SaveDeviceFailed implements BluetoothFailure {
  const factory SaveDeviceFailed() = _$SaveDeviceFailedImpl;
}

/// @nodoc
abstract class _$$GetSavedDeviceFailedImplCopyWith<$Res> {
  factory _$$GetSavedDeviceFailedImplCopyWith(_$GetSavedDeviceFailedImpl value,
          $Res Function(_$GetSavedDeviceFailedImpl) then) =
      __$$GetSavedDeviceFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetSavedDeviceFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$GetSavedDeviceFailedImpl>
    implements _$$GetSavedDeviceFailedImplCopyWith<$Res> {
  __$$GetSavedDeviceFailedImplCopyWithImpl(_$GetSavedDeviceFailedImpl _value,
      $Res Function(_$GetSavedDeviceFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetSavedDeviceFailedImpl implements GetSavedDeviceFailed {
  const _$GetSavedDeviceFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.getSavedDeviceFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetSavedDeviceFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return getSavedDeviceFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return getSavedDeviceFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (getSavedDeviceFailed != null) {
      return getSavedDeviceFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return getSavedDeviceFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return getSavedDeviceFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (getSavedDeviceFailed != null) {
      return getSavedDeviceFailed(this);
    }
    return orElse();
  }
}

abstract class GetSavedDeviceFailed implements BluetoothFailure {
  const factory GetSavedDeviceFailed() = _$GetSavedDeviceFailedImpl;
}

/// @nodoc
abstract class _$$SendCommandFailedImplCopyWith<$Res> {
  factory _$$SendCommandFailedImplCopyWith(_$SendCommandFailedImpl value,
          $Res Function(_$SendCommandFailedImpl) then) =
      __$$SendCommandFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SendCommandFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$SendCommandFailedImpl>
    implements _$$SendCommandFailedImplCopyWith<$Res> {
  __$$SendCommandFailedImplCopyWithImpl(_$SendCommandFailedImpl _value,
      $Res Function(_$SendCommandFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SendCommandFailedImpl implements SendCommandFailed {
  const _$SendCommandFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.sendCommandFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SendCommandFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return sendCommandFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return sendCommandFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (sendCommandFailed != null) {
      return sendCommandFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return sendCommandFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return sendCommandFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (sendCommandFailed != null) {
      return sendCommandFailed(this);
    }
    return orElse();
  }
}

abstract class SendCommandFailed implements BluetoothFailure {
  const factory SendCommandFailed() = _$SendCommandFailedImpl;
}

/// @nodoc
abstract class _$$GetCommandFailedImplCopyWith<$Res> {
  factory _$$GetCommandFailedImplCopyWith(_$GetCommandFailedImpl value,
          $Res Function(_$GetCommandFailedImpl) then) =
      __$$GetCommandFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetCommandFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$GetCommandFailedImpl>
    implements _$$GetCommandFailedImplCopyWith<$Res> {
  __$$GetCommandFailedImplCopyWithImpl(_$GetCommandFailedImpl _value,
      $Res Function(_$GetCommandFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetCommandFailedImpl implements GetCommandFailed {
  const _$GetCommandFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.getCommandFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetCommandFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return getCommandFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return getCommandFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (getCommandFailed != null) {
      return getCommandFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return getCommandFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return getCommandFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (getCommandFailed != null) {
      return getCommandFailed(this);
    }
    return orElse();
  }
}

abstract class GetCommandFailed implements BluetoothFailure {
  const factory GetCommandFailed() = _$GetCommandFailedImpl;
}

/// @nodoc
abstract class _$$ReconnectFailedImplCopyWith<$Res> {
  factory _$$ReconnectFailedImplCopyWith(_$ReconnectFailedImpl value,
          $Res Function(_$ReconnectFailedImpl) then) =
      __$$ReconnectFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ReconnectFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$ReconnectFailedImpl>
    implements _$$ReconnectFailedImplCopyWith<$Res> {
  __$$ReconnectFailedImplCopyWithImpl(
      _$ReconnectFailedImpl _value, $Res Function(_$ReconnectFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ReconnectFailedImpl implements ReconnectFailed {
  const _$ReconnectFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.reconnectFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ReconnectFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return reconnectFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return reconnectFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (reconnectFailed != null) {
      return reconnectFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return reconnectFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return reconnectFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (reconnectFailed != null) {
      return reconnectFailed(this);
    }
    return orElse();
  }
}

abstract class ReconnectFailed implements BluetoothFailure {
  const factory ReconnectFailed() = _$ReconnectFailedImpl;
}

/// @nodoc
abstract class _$$DisconnectionFailedImplCopyWith<$Res> {
  factory _$$DisconnectionFailedImplCopyWith(_$DisconnectionFailedImpl value,
          $Res Function(_$DisconnectionFailedImpl) then) =
      __$$DisconnectionFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisconnectionFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$DisconnectionFailedImpl>
    implements _$$DisconnectionFailedImplCopyWith<$Res> {
  __$$DisconnectionFailedImplCopyWithImpl(_$DisconnectionFailedImpl _value,
      $Res Function(_$DisconnectionFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DisconnectionFailedImpl implements DisconnectionFailed {
  const _$DisconnectionFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.disconnectionFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisconnectionFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return disconnectionFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return disconnectionFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (disconnectionFailed != null) {
      return disconnectionFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return disconnectionFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return disconnectionFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (disconnectionFailed != null) {
      return disconnectionFailed(this);
    }
    return orElse();
  }
}

abstract class DisconnectionFailed implements BluetoothFailure {
  const factory DisconnectionFailed() = _$DisconnectionFailedImpl;
}

/// @nodoc
abstract class _$$DeviceConnectionLostImplCopyWith<$Res> {
  factory _$$DeviceConnectionLostImplCopyWith(_$DeviceConnectionLostImpl value,
          $Res Function(_$DeviceConnectionLostImpl) then) =
      __$$DeviceConnectionLostImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeviceConnectionLostImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$DeviceConnectionLostImpl>
    implements _$$DeviceConnectionLostImplCopyWith<$Res> {
  __$$DeviceConnectionLostImplCopyWithImpl(_$DeviceConnectionLostImpl _value,
      $Res Function(_$DeviceConnectionLostImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeviceConnectionLostImpl implements DeviceConnectionLost {
  const _$DeviceConnectionLostImpl();

  @override
  String toString() {
    return 'BluetoothFailure.deviceConnectionLost()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceConnectionLostImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return deviceConnectionLost();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return deviceConnectionLost?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (deviceConnectionLost != null) {
      return deviceConnectionLost();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return deviceConnectionLost(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return deviceConnectionLost?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (deviceConnectionLost != null) {
      return deviceConnectionLost(this);
    }
    return orElse();
  }
}

abstract class DeviceConnectionLost implements BluetoothFailure {
  const factory DeviceConnectionLost() = _$DeviceConnectionLostImpl;
}

/// @nodoc
abstract class _$$StopScanningFailedImplCopyWith<$Res> {
  factory _$$StopScanningFailedImplCopyWith(_$StopScanningFailedImpl value,
          $Res Function(_$StopScanningFailedImpl) then) =
      __$$StopScanningFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StopScanningFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$StopScanningFailedImpl>
    implements _$$StopScanningFailedImplCopyWith<$Res> {
  __$$StopScanningFailedImplCopyWithImpl(_$StopScanningFailedImpl _value,
      $Res Function(_$StopScanningFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StopScanningFailedImpl implements StopScanningFailed {
  const _$StopScanningFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.stopScanningFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StopScanningFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return stopScanningFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return stopScanningFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (stopScanningFailed != null) {
      return stopScanningFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return stopScanningFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return stopScanningFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (stopScanningFailed != null) {
      return stopScanningFailed(this);
    }
    return orElse();
  }
}

abstract class StopScanningFailed implements BluetoothFailure {
  const factory StopScanningFailed() = _$StopScanningFailedImpl;
}

/// @nodoc
abstract class _$$OtaUpdateFailedImplCopyWith<$Res> {
  factory _$$OtaUpdateFailedImplCopyWith(_$OtaUpdateFailedImpl value,
          $Res Function(_$OtaUpdateFailedImpl) then) =
      __$$OtaUpdateFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtaUpdateFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$OtaUpdateFailedImpl>
    implements _$$OtaUpdateFailedImplCopyWith<$Res> {
  __$$OtaUpdateFailedImplCopyWithImpl(
      _$OtaUpdateFailedImpl _value, $Res Function(_$OtaUpdateFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OtaUpdateFailedImpl implements OtaUpdateFailed {
  const _$OtaUpdateFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.otaUpdateFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OtaUpdateFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return otaUpdateFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return otaUpdateFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaUpdateFailed != null) {
      return otaUpdateFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return otaUpdateFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return otaUpdateFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaUpdateFailed != null) {
      return otaUpdateFailed(this);
    }
    return orElse();
  }
}

abstract class OtaUpdateFailed implements BluetoothFailure {
  const factory OtaUpdateFailed() = _$OtaUpdateFailedImpl;
}

/// @nodoc
abstract class _$$OtaFileNotFoundImplCopyWith<$Res> {
  factory _$$OtaFileNotFoundImplCopyWith(_$OtaFileNotFoundImpl value,
          $Res Function(_$OtaFileNotFoundImpl) then) =
      __$$OtaFileNotFoundImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtaFileNotFoundImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$OtaFileNotFoundImpl>
    implements _$$OtaFileNotFoundImplCopyWith<$Res> {
  __$$OtaFileNotFoundImplCopyWithImpl(
      _$OtaFileNotFoundImpl _value, $Res Function(_$OtaFileNotFoundImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OtaFileNotFoundImpl implements OtaFileNotFound {
  const _$OtaFileNotFoundImpl();

  @override
  String toString() {
    return 'BluetoothFailure.otaFileNotFound()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OtaFileNotFoundImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return otaFileNotFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return otaFileNotFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaFileNotFound != null) {
      return otaFileNotFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return otaFileNotFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return otaFileNotFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaFileNotFound != null) {
      return otaFileNotFound(this);
    }
    return orElse();
  }
}

abstract class OtaFileNotFound implements BluetoothFailure {
  const factory OtaFileNotFound() = _$OtaFileNotFoundImpl;
}

/// @nodoc
abstract class _$$OtaInvalidFirmwareImplCopyWith<$Res> {
  factory _$$OtaInvalidFirmwareImplCopyWith(_$OtaInvalidFirmwareImpl value,
          $Res Function(_$OtaInvalidFirmwareImpl) then) =
      __$$OtaInvalidFirmwareImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtaInvalidFirmwareImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$OtaInvalidFirmwareImpl>
    implements _$$OtaInvalidFirmwareImplCopyWith<$Res> {
  __$$OtaInvalidFirmwareImplCopyWithImpl(_$OtaInvalidFirmwareImpl _value,
      $Res Function(_$OtaInvalidFirmwareImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OtaInvalidFirmwareImpl implements OtaInvalidFirmware {
  const _$OtaInvalidFirmwareImpl();

  @override
  String toString() {
    return 'BluetoothFailure.otaInvalidFirmware()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OtaInvalidFirmwareImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return otaInvalidFirmware();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return otaInvalidFirmware?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaInvalidFirmware != null) {
      return otaInvalidFirmware();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return otaInvalidFirmware(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return otaInvalidFirmware?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaInvalidFirmware != null) {
      return otaInvalidFirmware(this);
    }
    return orElse();
  }
}

abstract class OtaInvalidFirmware implements BluetoothFailure {
  const factory OtaInvalidFirmware() = _$OtaInvalidFirmwareImpl;
}

/// @nodoc
abstract class _$$OtaTransferFailedImplCopyWith<$Res> {
  factory _$$OtaTransferFailedImplCopyWith(_$OtaTransferFailedImpl value,
          $Res Function(_$OtaTransferFailedImpl) then) =
      __$$OtaTransferFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtaTransferFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$OtaTransferFailedImpl>
    implements _$$OtaTransferFailedImplCopyWith<$Res> {
  __$$OtaTransferFailedImplCopyWithImpl(_$OtaTransferFailedImpl _value,
      $Res Function(_$OtaTransferFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OtaTransferFailedImpl implements OtaTransferFailed {
  const _$OtaTransferFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.otaTransferFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OtaTransferFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return otaTransferFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return otaTransferFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaTransferFailed != null) {
      return otaTransferFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return otaTransferFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return otaTransferFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaTransferFailed != null) {
      return otaTransferFailed(this);
    }
    return orElse();
  }
}

abstract class OtaTransferFailed implements BluetoothFailure {
  const factory OtaTransferFailed() = _$OtaTransferFailedImpl;
}

/// @nodoc
abstract class _$$OtaVerificationFailedImplCopyWith<$Res> {
  factory _$$OtaVerificationFailedImplCopyWith(
          _$OtaVerificationFailedImpl value,
          $Res Function(_$OtaVerificationFailedImpl) then) =
      __$$OtaVerificationFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtaVerificationFailedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$OtaVerificationFailedImpl>
    implements _$$OtaVerificationFailedImplCopyWith<$Res> {
  __$$OtaVerificationFailedImplCopyWithImpl(_$OtaVerificationFailedImpl _value,
      $Res Function(_$OtaVerificationFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OtaVerificationFailedImpl implements OtaVerificationFailed {
  const _$OtaVerificationFailedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.otaVerificationFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtaVerificationFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return otaVerificationFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return otaVerificationFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaVerificationFailed != null) {
      return otaVerificationFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return otaVerificationFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return otaVerificationFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaVerificationFailed != null) {
      return otaVerificationFailed(this);
    }
    return orElse();
  }
}

abstract class OtaVerificationFailed implements BluetoothFailure {
  const factory OtaVerificationFailed() = _$OtaVerificationFailedImpl;
}

/// @nodoc
abstract class _$$OtaInsufficientSpaceImplCopyWith<$Res> {
  factory _$$OtaInsufficientSpaceImplCopyWith(_$OtaInsufficientSpaceImpl value,
          $Res Function(_$OtaInsufficientSpaceImpl) then) =
      __$$OtaInsufficientSpaceImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtaInsufficientSpaceImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$OtaInsufficientSpaceImpl>
    implements _$$OtaInsufficientSpaceImplCopyWith<$Res> {
  __$$OtaInsufficientSpaceImplCopyWithImpl(_$OtaInsufficientSpaceImpl _value,
      $Res Function(_$OtaInsufficientSpaceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OtaInsufficientSpaceImpl implements OtaInsufficientSpace {
  const _$OtaInsufficientSpaceImpl();

  @override
  String toString() {
    return 'BluetoothFailure.otaInsufficientSpace()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtaInsufficientSpaceImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return otaInsufficientSpace();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return otaInsufficientSpace?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaInsufficientSpace != null) {
      return otaInsufficientSpace();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return otaInsufficientSpace(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return otaInsufficientSpace?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaInsufficientSpace != null) {
      return otaInsufficientSpace(this);
    }
    return orElse();
  }
}

abstract class OtaInsufficientSpace implements BluetoothFailure {
  const factory OtaInsufficientSpace() = _$OtaInsufficientSpaceImpl;
}

/// @nodoc
abstract class _$$OtaDeviceNotSupportedImplCopyWith<$Res> {
  factory _$$OtaDeviceNotSupportedImplCopyWith(
          _$OtaDeviceNotSupportedImpl value,
          $Res Function(_$OtaDeviceNotSupportedImpl) then) =
      __$$OtaDeviceNotSupportedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OtaDeviceNotSupportedImplCopyWithImpl<$Res>
    extends _$BluetoothFailureCopyWithImpl<$Res, _$OtaDeviceNotSupportedImpl>
    implements _$$OtaDeviceNotSupportedImplCopyWith<$Res> {
  __$$OtaDeviceNotSupportedImplCopyWithImpl(_$OtaDeviceNotSupportedImpl _value,
      $Res Function(_$OtaDeviceNotSupportedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BluetoothFailure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OtaDeviceNotSupportedImpl implements OtaDeviceNotSupported {
  const _$OtaDeviceNotSupportedImpl();

  @override
  String toString() {
    return 'BluetoothFailure.otaDeviceNotSupported()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtaDeviceNotSupportedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() bluetoothDisabled,
    required TResult Function() deviceConnectionFailed,
    required TResult Function() noDevicesFound,
    required TResult Function() searchTimeout,
    required TResult Function() permissionDenied,
    required TResult Function() unexpected,
    required TResult Function() saveDeviceFailed,
    required TResult Function() getSavedDeviceFailed,
    required TResult Function() sendCommandFailed,
    required TResult Function() getCommandFailed,
    required TResult Function() reconnectFailed,
    required TResult Function() disconnectionFailed,
    required TResult Function() deviceConnectionLost,
    required TResult Function() stopScanningFailed,
    required TResult Function() otaUpdateFailed,
    required TResult Function() otaFileNotFound,
    required TResult Function() otaInvalidFirmware,
    required TResult Function() otaTransferFailed,
    required TResult Function() otaVerificationFailed,
    required TResult Function() otaInsufficientSpace,
    required TResult Function() otaDeviceNotSupported,
  }) {
    return otaDeviceNotSupported();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? bluetoothDisabled,
    TResult? Function()? deviceConnectionFailed,
    TResult? Function()? noDevicesFound,
    TResult? Function()? searchTimeout,
    TResult? Function()? permissionDenied,
    TResult? Function()? unexpected,
    TResult? Function()? saveDeviceFailed,
    TResult? Function()? getSavedDeviceFailed,
    TResult? Function()? sendCommandFailed,
    TResult? Function()? getCommandFailed,
    TResult? Function()? reconnectFailed,
    TResult? Function()? disconnectionFailed,
    TResult? Function()? deviceConnectionLost,
    TResult? Function()? stopScanningFailed,
    TResult? Function()? otaUpdateFailed,
    TResult? Function()? otaFileNotFound,
    TResult? Function()? otaInvalidFirmware,
    TResult? Function()? otaTransferFailed,
    TResult? Function()? otaVerificationFailed,
    TResult? Function()? otaInsufficientSpace,
    TResult? Function()? otaDeviceNotSupported,
  }) {
    return otaDeviceNotSupported?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? bluetoothDisabled,
    TResult Function()? deviceConnectionFailed,
    TResult Function()? noDevicesFound,
    TResult Function()? searchTimeout,
    TResult Function()? permissionDenied,
    TResult Function()? unexpected,
    TResult Function()? saveDeviceFailed,
    TResult Function()? getSavedDeviceFailed,
    TResult Function()? sendCommandFailed,
    TResult Function()? getCommandFailed,
    TResult Function()? reconnectFailed,
    TResult Function()? disconnectionFailed,
    TResult Function()? deviceConnectionLost,
    TResult Function()? stopScanningFailed,
    TResult Function()? otaUpdateFailed,
    TResult Function()? otaFileNotFound,
    TResult Function()? otaInvalidFirmware,
    TResult Function()? otaTransferFailed,
    TResult Function()? otaVerificationFailed,
    TResult Function()? otaInsufficientSpace,
    TResult Function()? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaDeviceNotSupported != null) {
      return otaDeviceNotSupported();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BluetoothDisabled value) bluetoothDisabled,
    required TResult Function(DeviceConnectionFailed value)
        deviceConnectionFailed,
    required TResult Function(NoDevicesFound value) noDevicesFound,
    required TResult Function(SearchTimeout value) searchTimeout,
    required TResult Function(PermissionDenied value) permissionDenied,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(SaveDeviceFailed value) saveDeviceFailed,
    required TResult Function(GetSavedDeviceFailed value) getSavedDeviceFailed,
    required TResult Function(SendCommandFailed value) sendCommandFailed,
    required TResult Function(GetCommandFailed value) getCommandFailed,
    required TResult Function(ReconnectFailed value) reconnectFailed,
    required TResult Function(DisconnectionFailed value) disconnectionFailed,
    required TResult Function(DeviceConnectionLost value) deviceConnectionLost,
    required TResult Function(StopScanningFailed value) stopScanningFailed,
    required TResult Function(OtaUpdateFailed value) otaUpdateFailed,
    required TResult Function(OtaFileNotFound value) otaFileNotFound,
    required TResult Function(OtaInvalidFirmware value) otaInvalidFirmware,
    required TResult Function(OtaTransferFailed value) otaTransferFailed,
    required TResult Function(OtaVerificationFailed value)
        otaVerificationFailed,
    required TResult Function(OtaInsufficientSpace value) otaInsufficientSpace,
    required TResult Function(OtaDeviceNotSupported value)
        otaDeviceNotSupported,
  }) {
    return otaDeviceNotSupported(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult? Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult? Function(NoDevicesFound value)? noDevicesFound,
    TResult? Function(SearchTimeout value)? searchTimeout,
    TResult? Function(PermissionDenied value)? permissionDenied,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult? Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult? Function(SendCommandFailed value)? sendCommandFailed,
    TResult? Function(GetCommandFailed value)? getCommandFailed,
    TResult? Function(ReconnectFailed value)? reconnectFailed,
    TResult? Function(DisconnectionFailed value)? disconnectionFailed,
    TResult? Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult? Function(StopScanningFailed value)? stopScanningFailed,
    TResult? Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult? Function(OtaFileNotFound value)? otaFileNotFound,
    TResult? Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult? Function(OtaTransferFailed value)? otaTransferFailed,
    TResult? Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult? Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult? Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
  }) {
    return otaDeviceNotSupported?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BluetoothDisabled value)? bluetoothDisabled,
    TResult Function(DeviceConnectionFailed value)? deviceConnectionFailed,
    TResult Function(NoDevicesFound value)? noDevicesFound,
    TResult Function(SearchTimeout value)? searchTimeout,
    TResult Function(PermissionDenied value)? permissionDenied,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(SaveDeviceFailed value)? saveDeviceFailed,
    TResult Function(GetSavedDeviceFailed value)? getSavedDeviceFailed,
    TResult Function(SendCommandFailed value)? sendCommandFailed,
    TResult Function(GetCommandFailed value)? getCommandFailed,
    TResult Function(ReconnectFailed value)? reconnectFailed,
    TResult Function(DisconnectionFailed value)? disconnectionFailed,
    TResult Function(DeviceConnectionLost value)? deviceConnectionLost,
    TResult Function(StopScanningFailed value)? stopScanningFailed,
    TResult Function(OtaUpdateFailed value)? otaUpdateFailed,
    TResult Function(OtaFileNotFound value)? otaFileNotFound,
    TResult Function(OtaInvalidFirmware value)? otaInvalidFirmware,
    TResult Function(OtaTransferFailed value)? otaTransferFailed,
    TResult Function(OtaVerificationFailed value)? otaVerificationFailed,
    TResult Function(OtaInsufficientSpace value)? otaInsufficientSpace,
    TResult Function(OtaDeviceNotSupported value)? otaDeviceNotSupported,
    required TResult orElse(),
  }) {
    if (otaDeviceNotSupported != null) {
      return otaDeviceNotSupported(this);
    }
    return orElse();
  }
}

abstract class OtaDeviceNotSupported implements BluetoothFailure {
  const factory OtaDeviceNotSupported() = _$OtaDeviceNotSupportedImpl;
}
