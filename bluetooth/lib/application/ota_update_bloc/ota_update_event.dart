part of 'ota_update_bloc.dart';

/// Events for OTA Update Bloc
@freezed
class OtaUpdateEvent with _$OtaUpdateEvent {
  const factory OtaUpdateEvent.checkDeviceFirmware(BluetoothDevice device) =
      CheckDeviceFirmware;
  const factory OtaUpdateEvent.checkForUpdates(BluetoothDevice device) =
      CheckForUpdates;
  const factory OtaUpdateEvent.startOtaUpdate(
      BluetoothDevice device, Uint8List firmwareData) = StartOtaUpdate;
  const factory OtaUpdateEvent.cancelOtaUpdate(BluetoothDevice device) =
      CancelOtaUpdate;
  const factory OtaUpdateEvent.loadUpdateHistory() = LoadUpdateHistory;
  const factory OtaUpdateEvent.verifyFirmwareFile(
      BluetoothDevice device, Uint8List firmwareData) = VerifyFirmwareFile;
  const factory OtaUpdateEvent.prepareDeviceForOta(BluetoothDevice device) =
      PrepareDeviceForOta;

  // Internal events
  const factory OtaUpdateEvent.otaProgressReceived(OtaProgress progress) =
      OtaProgressReceived;
  const factory OtaUpdateEvent.otaErrorReceived(BluetoothFailure failure) =
      OtaErrorReceived;
}
