import 'dart:async';
import 'dart:typed_data';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/ota_facade.dart';
import '../../domain/failure/bluetooth_failure.dart';

part 'ota_update_bloc.freezed.dart';
part 'ota_update_event.dart';
part 'ota_update_state.dart';

@injectable
class OtaUpdateBloc extends Bloc<OtaUpdateEvent, OtaUpdateState> {
  final IOtaFacade _otaFacade;
  StreamSubscription<Either<BluetoothFailure, OtaProgress>>?
      _progressSubscription;

  OtaUpdateBloc(this._otaFacade) : super(const OtaUpdateState.initial()) {
    on<CheckDeviceFirmware>(_onCheckDeviceFirmware);
    on<CheckForUpdates>(_onCheckForUpdates);
    on<StartOtaUpdate>(_onStartOtaUpdate);
    on<CancelOtaUpdate>(_onCancelOtaUpdate);
    on<LoadUpdateHistory>(_onLoadUpdateHistory);
    on<VerifyFirmwareFile>(_onVerifyFirmwareFile);
    on<PrepareDeviceForOta>(_onPrepareDeviceForOta);
    on<OtaProgressReceived>(_onOtaProgressReceived);
    on<OtaErrorReceived>(_onOtaErrorReceived);
  }

  /// Handles checking device firmware information
  Future<void> _onCheckDeviceFirmware(
    CheckDeviceFirmware event,
    Emitter<OtaUpdateState> emit,
  ) async {
    emit(const OtaUpdateState.checkingFirmware());

    final result = await _otaFacade.getDeviceFirmwareInfo(event.device);

    result.mapBoth(
      onLeft: (failure) => emit(OtaUpdateState.error(failure)),
      onRight: (firmwareInfo) =>
          emit(OtaUpdateState.firmwareInfoLoaded(firmwareInfo)),
    );
  }

  /// Handles checking for available firmware updates
  Future<void> _onCheckForUpdates(
    CheckForUpdates event,
    Emitter<OtaUpdateState> emit,
  ) async {
    emit(const OtaUpdateState.checkingForUpdates());

    final result = await _otaFacade.checkForUpdates(event.device);

    result.mapBoth(
      onLeft: (failure) => emit(OtaUpdateState.error(failure)),
      onRight: (updateInfo) {
        if (updateInfo != null) {
          emit(OtaUpdateState.updateAvailable(updateInfo));
        } else {
          emit(const OtaUpdateState.noUpdatesAvailable());
        }
      },
    );
  }

  /// Handles starting the OTA update process
  Future<void> _onStartOtaUpdate(
    StartOtaUpdate event,
    Emitter<OtaUpdateState> emit,
  ) async {
    emit(const OtaUpdateState.preparingUpdate());

    // First prepare the device for OTA
    final prepareResult = await _otaFacade.prepareDeviceForOta(event.device);

    bool shouldContinue = false;
    prepareResult.mapBoth(
      onLeft: (failure) {
        emit(OtaUpdateState.error(failure));
        shouldContinue = false;
      },
      onRight: (_) {
        shouldContinue = true;
      },
    );

    if (!shouldContinue) return;

    // Verify firmware file
    final verifyResult = await _otaFacade.verifyFirmwareFile(
      event.firmwareData,
      event.device,
    );

    bool isValid = false;
    verifyResult.mapBoth(
      onLeft: (failure) {
        emit(OtaUpdateState.error(failure));
        isValid = false;
      },
      onRight: (valid) {
        isValid = valid;
      },
    );

    if (!isValid) return;

    // Start the OTA update process
    emit(const OtaUpdateState.updateInProgress(
      OtaProgress(
        bytesTransferred: 0,
        totalBytes: 0,
        percentage: 0.0,
        status: 'Starting update...',
      ),
    ));

    // Listen to progress updates
    _progressSubscription?.cancel();
    _progressSubscription =
        _otaFacade.startOtaUpdate(event.device, event.firmwareData).listen(
      (result) {
        result.mapBoth(
          onLeft: (failure) => add(OtaUpdateEvent.otaErrorReceived(failure)),
          onRight: (progress) =>
              add(OtaUpdateEvent.otaProgressReceived(progress)),
        );
      },
      onError: (error) {
        add(const OtaUpdateEvent.otaErrorReceived(
            BluetoothFailure.otaUpdateFailed()));
      },
      onDone: () {
        // Update completed successfully
        _finalizeUpdate(event.device);
      },
    );
  }

  /// Handles cancelling the OTA update
  Future<void> _onCancelOtaUpdate(
    CancelOtaUpdate event,
    Emitter<OtaUpdateState> emit,
  ) async {
    emit(const OtaUpdateState.cancellingUpdate());

    _progressSubscription?.cancel();
    _progressSubscription = null;

    final result = await _otaFacade.cancelOtaUpdate(event.device);

    result.mapBoth(
      onLeft: (failure) => emit(OtaUpdateState.error(failure)),
      onRight: (_) => emit(const OtaUpdateState.updateCancelled()),
    );
  }

  /// Handles loading update history
  Future<void> _onLoadUpdateHistory(
    LoadUpdateHistory event,
    Emitter<OtaUpdateState> emit,
  ) async {
    emit(const OtaUpdateState.loadingHistory());

    final result = await _otaFacade.getUpdateHistory();

    result.mapBoth(
      onLeft: (failure) => emit(OtaUpdateState.error(failure)),
      onRight: (history) => emit(OtaUpdateState.historyLoaded(history)),
    );
  }

  /// Handles firmware file verification
  Future<void> _onVerifyFirmwareFile(
    VerifyFirmwareFile event,
    Emitter<OtaUpdateState> emit,
  ) async {
    emit(const OtaUpdateState.verifyingFirmware());

    final result = await _otaFacade.verifyFirmwareFile(
      event.firmwareData,
      event.device,
    );

    result.mapBoth(
      onLeft: (failure) => emit(OtaUpdateState.error(failure)),
      onRight: (isValid) {
        if (isValid) {
          emit(const OtaUpdateState.firmwareVerified());
        } else {
          emit(const OtaUpdateState.error(
              BluetoothFailure.otaInvalidFirmware()));
        }
      },
    );
  }

  /// Handles device preparation for OTA
  Future<void> _onPrepareDeviceForOta(
    PrepareDeviceForOta event,
    Emitter<OtaUpdateState> emit,
  ) async {
    emit(const OtaUpdateState.preparingDevice());

    final result = await _otaFacade.prepareDeviceForOta(event.device);

    result.mapBoth(
      onLeft: (failure) => emit(OtaUpdateState.error(failure)),
      onRight: (_) => emit(const OtaUpdateState.devicePrepared()),
    );
  }

  /// Handles OTA progress updates
  void _onOtaProgressReceived(
    OtaProgressReceived event,
    Emitter<OtaUpdateState> emit,
  ) {
    emit(OtaUpdateState.updateInProgress(event.progress));
  }

  /// Handles OTA errors
  void _onOtaErrorReceived(
    OtaErrorReceived event,
    Emitter<OtaUpdateState> emit,
  ) {
    emit(OtaUpdateState.error(event.failure));
  }

  /// Finalizes the OTA update process
  Future<void> _finalizeUpdate(BluetoothDevice device) async {
    final result = await _otaFacade.finalizeOtaUpdate(device);

    result.mapBoth(
      onLeft: (failure) => add(OtaUpdateEvent.otaErrorReceived(failure)),
      onRight: (_) {
        // Update completed successfully - we need to emit this through an event
        // For now, we'll handle this in the stream completion
      },
    );
  }

  @override
  Future<void> close() {
    _progressSubscription?.cancel();
    return super.close();
  }
}
