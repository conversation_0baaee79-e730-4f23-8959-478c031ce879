part of 'ota_update_bloc.dart';

/// States for OTA Update Bloc
@freezed
class OtaUpdateState with _$OtaUpdateState {
  const factory OtaUpdateState.initial() = _Initial;
  const factory OtaUpdateState.checkingFirmware() = _CheckingFirmware;
  const factory OtaUpdateState.firmwareInfoLoaded(DeviceFirmwareInfo firmwareInfo) = _FirmwareInfoLoaded;
  const factory OtaUpdateState.checkingForUpdates() = _CheckingForUpdates;
  const factory OtaUpdateState.updateAvailable(FirmwareInfo updateInfo) = _UpdateAvailable;
  const factory OtaUpdateState.noUpdatesAvailable() = _NoUpdatesAvailable;
  const factory OtaUpdateState.verifyingFirmware() = _VerifyingFirmware;
  const factory OtaUpdateState.firmwareVerified() = _FirmwareVerified;
  const factory OtaUpdateState.preparingDevice() = _PreparingDevice;
  const factory OtaUpdateState.devicePrepared() = _DevicePrepared;
  const factory OtaUpdateState.preparingUpdate() = _PreparingUpdate;
  const factory OtaUpdateState.updateInProgress(OtaProgress progress) = _UpdateInProgress;
  const factory OtaUpdateState.updateCompleted() = _UpdateCompleted;
  const factory OtaUpdateState.cancellingUpdate() = _CancellingUpdate;
  const factory OtaUpdateState.updateCancelled() = _UpdateCancelled;
  const factory OtaUpdateState.loadingHistory() = _LoadingHistory;
  const factory OtaUpdateState.historyLoaded(List<OtaUpdateRecord> history) = _HistoryLoaded;
  const factory OtaUpdateState.error(BluetoothFailure failure) = _Error;
}
