// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ota_update_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OtaUpdateEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtaUpdateEventCopyWith<$Res> {
  factory $OtaUpdateEventCopyWith(
          OtaUpdateEvent value, $Res Function(OtaUpdateEvent) then) =
      _$OtaUpdateEventCopyWithImpl<$Res, OtaUpdateEvent>;
}

/// @nodoc
class _$OtaUpdateEventCopyWithImpl<$Res, $Val extends OtaUpdateEvent>
    implements $OtaUpdateEventCopyWith<$Res> {
  _$OtaUpdateEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CheckDeviceFirmwareImplCopyWith<$Res> {
  factory _$$CheckDeviceFirmwareImplCopyWith(_$CheckDeviceFirmwareImpl value,
          $Res Function(_$CheckDeviceFirmwareImpl) then) =
      __$$CheckDeviceFirmwareImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$CheckDeviceFirmwareImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$CheckDeviceFirmwareImpl>
    implements _$$CheckDeviceFirmwareImplCopyWith<$Res> {
  __$$CheckDeviceFirmwareImplCopyWithImpl(_$CheckDeviceFirmwareImpl _value,
      $Res Function(_$CheckDeviceFirmwareImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$CheckDeviceFirmwareImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$CheckDeviceFirmwareImpl implements CheckDeviceFirmware {
  const _$CheckDeviceFirmwareImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'OtaUpdateEvent.checkDeviceFirmware(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckDeviceFirmwareImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckDeviceFirmwareImplCopyWith<_$CheckDeviceFirmwareImpl> get copyWith =>
      __$$CheckDeviceFirmwareImplCopyWithImpl<_$CheckDeviceFirmwareImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return checkDeviceFirmware(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return checkDeviceFirmware?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (checkDeviceFirmware != null) {
      return checkDeviceFirmware(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return checkDeviceFirmware(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return checkDeviceFirmware?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (checkDeviceFirmware != null) {
      return checkDeviceFirmware(this);
    }
    return orElse();
  }
}

abstract class CheckDeviceFirmware implements OtaUpdateEvent {
  const factory CheckDeviceFirmware(final BluetoothDevice device) =
      _$CheckDeviceFirmwareImpl;

  BluetoothDevice get device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckDeviceFirmwareImplCopyWith<_$CheckDeviceFirmwareImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CheckForUpdatesImplCopyWith<$Res> {
  factory _$$CheckForUpdatesImplCopyWith(_$CheckForUpdatesImpl value,
          $Res Function(_$CheckForUpdatesImpl) then) =
      __$$CheckForUpdatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$CheckForUpdatesImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$CheckForUpdatesImpl>
    implements _$$CheckForUpdatesImplCopyWith<$Res> {
  __$$CheckForUpdatesImplCopyWithImpl(
      _$CheckForUpdatesImpl _value, $Res Function(_$CheckForUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$CheckForUpdatesImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$CheckForUpdatesImpl implements CheckForUpdates {
  const _$CheckForUpdatesImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'OtaUpdateEvent.checkForUpdates(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckForUpdatesImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckForUpdatesImplCopyWith<_$CheckForUpdatesImpl> get copyWith =>
      __$$CheckForUpdatesImplCopyWithImpl<_$CheckForUpdatesImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return checkForUpdates(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return checkForUpdates?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (checkForUpdates != null) {
      return checkForUpdates(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return checkForUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return checkForUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (checkForUpdates != null) {
      return checkForUpdates(this);
    }
    return orElse();
  }
}

abstract class CheckForUpdates implements OtaUpdateEvent {
  const factory CheckForUpdates(final BluetoothDevice device) =
      _$CheckForUpdatesImpl;

  BluetoothDevice get device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckForUpdatesImplCopyWith<_$CheckForUpdatesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartOtaUpdateImplCopyWith<$Res> {
  factory _$$StartOtaUpdateImplCopyWith(_$StartOtaUpdateImpl value,
          $Res Function(_$StartOtaUpdateImpl) then) =
      __$$StartOtaUpdateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device, Uint8List firmwareData});
}

/// @nodoc
class __$$StartOtaUpdateImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$StartOtaUpdateImpl>
    implements _$$StartOtaUpdateImplCopyWith<$Res> {
  __$$StartOtaUpdateImplCopyWithImpl(
      _$StartOtaUpdateImpl _value, $Res Function(_$StartOtaUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
    Object? firmwareData = null,
  }) {
    return _then(_$StartOtaUpdateImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
      null == firmwareData
          ? _value.firmwareData
          : firmwareData // ignore: cast_nullable_to_non_nullable
              as Uint8List,
    ));
  }
}

/// @nodoc

class _$StartOtaUpdateImpl implements StartOtaUpdate {
  const _$StartOtaUpdateImpl(this.device, this.firmwareData);

  @override
  final BluetoothDevice device;
  @override
  final Uint8List firmwareData;

  @override
  String toString() {
    return 'OtaUpdateEvent.startOtaUpdate(device: $device, firmwareData: $firmwareData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartOtaUpdateImpl &&
            (identical(other.device, device) || other.device == device) &&
            const DeepCollectionEquality()
                .equals(other.firmwareData, firmwareData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, device, const DeepCollectionEquality().hash(firmwareData));

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartOtaUpdateImplCopyWith<_$StartOtaUpdateImpl> get copyWith =>
      __$$StartOtaUpdateImplCopyWithImpl<_$StartOtaUpdateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return startOtaUpdate(device, firmwareData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return startOtaUpdate?.call(device, firmwareData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (startOtaUpdate != null) {
      return startOtaUpdate(device, firmwareData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return startOtaUpdate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return startOtaUpdate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (startOtaUpdate != null) {
      return startOtaUpdate(this);
    }
    return orElse();
  }
}

abstract class StartOtaUpdate implements OtaUpdateEvent {
  const factory StartOtaUpdate(
          final BluetoothDevice device, final Uint8List firmwareData) =
      _$StartOtaUpdateImpl;

  BluetoothDevice get device;
  Uint8List get firmwareData;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartOtaUpdateImplCopyWith<_$StartOtaUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CancelOtaUpdateImplCopyWith<$Res> {
  factory _$$CancelOtaUpdateImplCopyWith(_$CancelOtaUpdateImpl value,
          $Res Function(_$CancelOtaUpdateImpl) then) =
      __$$CancelOtaUpdateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$CancelOtaUpdateImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$CancelOtaUpdateImpl>
    implements _$$CancelOtaUpdateImplCopyWith<$Res> {
  __$$CancelOtaUpdateImplCopyWithImpl(
      _$CancelOtaUpdateImpl _value, $Res Function(_$CancelOtaUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$CancelOtaUpdateImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$CancelOtaUpdateImpl implements CancelOtaUpdate {
  const _$CancelOtaUpdateImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'OtaUpdateEvent.cancelOtaUpdate(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CancelOtaUpdateImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CancelOtaUpdateImplCopyWith<_$CancelOtaUpdateImpl> get copyWith =>
      __$$CancelOtaUpdateImplCopyWithImpl<_$CancelOtaUpdateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return cancelOtaUpdate(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return cancelOtaUpdate?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (cancelOtaUpdate != null) {
      return cancelOtaUpdate(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return cancelOtaUpdate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return cancelOtaUpdate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (cancelOtaUpdate != null) {
      return cancelOtaUpdate(this);
    }
    return orElse();
  }
}

abstract class CancelOtaUpdate implements OtaUpdateEvent {
  const factory CancelOtaUpdate(final BluetoothDevice device) =
      _$CancelOtaUpdateImpl;

  BluetoothDevice get device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CancelOtaUpdateImplCopyWith<_$CancelOtaUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadUpdateHistoryImplCopyWith<$Res> {
  factory _$$LoadUpdateHistoryImplCopyWith(_$LoadUpdateHistoryImpl value,
          $Res Function(_$LoadUpdateHistoryImpl) then) =
      __$$LoadUpdateHistoryImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadUpdateHistoryImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$LoadUpdateHistoryImpl>
    implements _$$LoadUpdateHistoryImplCopyWith<$Res> {
  __$$LoadUpdateHistoryImplCopyWithImpl(_$LoadUpdateHistoryImpl _value,
      $Res Function(_$LoadUpdateHistoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadUpdateHistoryImpl implements LoadUpdateHistory {
  const _$LoadUpdateHistoryImpl();

  @override
  String toString() {
    return 'OtaUpdateEvent.loadUpdateHistory()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadUpdateHistoryImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return loadUpdateHistory();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return loadUpdateHistory?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (loadUpdateHistory != null) {
      return loadUpdateHistory();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return loadUpdateHistory(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return loadUpdateHistory?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (loadUpdateHistory != null) {
      return loadUpdateHistory(this);
    }
    return orElse();
  }
}

abstract class LoadUpdateHistory implements OtaUpdateEvent {
  const factory LoadUpdateHistory() = _$LoadUpdateHistoryImpl;
}

/// @nodoc
abstract class _$$VerifyFirmwareFileImplCopyWith<$Res> {
  factory _$$VerifyFirmwareFileImplCopyWith(_$VerifyFirmwareFileImpl value,
          $Res Function(_$VerifyFirmwareFileImpl) then) =
      __$$VerifyFirmwareFileImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device, Uint8List firmwareData});
}

/// @nodoc
class __$$VerifyFirmwareFileImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$VerifyFirmwareFileImpl>
    implements _$$VerifyFirmwareFileImplCopyWith<$Res> {
  __$$VerifyFirmwareFileImplCopyWithImpl(_$VerifyFirmwareFileImpl _value,
      $Res Function(_$VerifyFirmwareFileImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
    Object? firmwareData = null,
  }) {
    return _then(_$VerifyFirmwareFileImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
      null == firmwareData
          ? _value.firmwareData
          : firmwareData // ignore: cast_nullable_to_non_nullable
              as Uint8List,
    ));
  }
}

/// @nodoc

class _$VerifyFirmwareFileImpl implements VerifyFirmwareFile {
  const _$VerifyFirmwareFileImpl(this.device, this.firmwareData);

  @override
  final BluetoothDevice device;
  @override
  final Uint8List firmwareData;

  @override
  String toString() {
    return 'OtaUpdateEvent.verifyFirmwareFile(device: $device, firmwareData: $firmwareData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyFirmwareFileImpl &&
            (identical(other.device, device) || other.device == device) &&
            const DeepCollectionEquality()
                .equals(other.firmwareData, firmwareData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, device, const DeepCollectionEquality().hash(firmwareData));

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyFirmwareFileImplCopyWith<_$VerifyFirmwareFileImpl> get copyWith =>
      __$$VerifyFirmwareFileImplCopyWithImpl<_$VerifyFirmwareFileImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return verifyFirmwareFile(device, firmwareData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return verifyFirmwareFile?.call(device, firmwareData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (verifyFirmwareFile != null) {
      return verifyFirmwareFile(device, firmwareData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return verifyFirmwareFile(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return verifyFirmwareFile?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (verifyFirmwareFile != null) {
      return verifyFirmwareFile(this);
    }
    return orElse();
  }
}

abstract class VerifyFirmwareFile implements OtaUpdateEvent {
  const factory VerifyFirmwareFile(
          final BluetoothDevice device, final Uint8List firmwareData) =
      _$VerifyFirmwareFileImpl;

  BluetoothDevice get device;
  Uint8List get firmwareData;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyFirmwareFileImplCopyWith<_$VerifyFirmwareFileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PrepareDeviceForOtaImplCopyWith<$Res> {
  factory _$$PrepareDeviceForOtaImplCopyWith(_$PrepareDeviceForOtaImpl value,
          $Res Function(_$PrepareDeviceForOtaImpl) then) =
      __$$PrepareDeviceForOtaImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class __$$PrepareDeviceForOtaImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$PrepareDeviceForOtaImpl>
    implements _$$PrepareDeviceForOtaImplCopyWith<$Res> {
  __$$PrepareDeviceForOtaImplCopyWithImpl(_$PrepareDeviceForOtaImpl _value,
      $Res Function(_$PrepareDeviceForOtaImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? device = null,
  }) {
    return _then(_$PrepareDeviceForOtaImpl(
      null == device
          ? _value.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class _$PrepareDeviceForOtaImpl implements PrepareDeviceForOta {
  const _$PrepareDeviceForOtaImpl(this.device);

  @override
  final BluetoothDevice device;

  @override
  String toString() {
    return 'OtaUpdateEvent.prepareDeviceForOta(device: $device)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PrepareDeviceForOtaImpl &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PrepareDeviceForOtaImplCopyWith<_$PrepareDeviceForOtaImpl> get copyWith =>
      __$$PrepareDeviceForOtaImplCopyWithImpl<_$PrepareDeviceForOtaImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return prepareDeviceForOta(device);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return prepareDeviceForOta?.call(device);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (prepareDeviceForOta != null) {
      return prepareDeviceForOta(device);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return prepareDeviceForOta(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return prepareDeviceForOta?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (prepareDeviceForOta != null) {
      return prepareDeviceForOta(this);
    }
    return orElse();
  }
}

abstract class PrepareDeviceForOta implements OtaUpdateEvent {
  const factory PrepareDeviceForOta(final BluetoothDevice device) =
      _$PrepareDeviceForOtaImpl;

  BluetoothDevice get device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PrepareDeviceForOtaImplCopyWith<_$PrepareDeviceForOtaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OtaProgressReceivedImplCopyWith<$Res> {
  factory _$$OtaProgressReceivedImplCopyWith(_$OtaProgressReceivedImpl value,
          $Res Function(_$OtaProgressReceivedImpl) then) =
      __$$OtaProgressReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({OtaProgress progress});
}

/// @nodoc
class __$$OtaProgressReceivedImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$OtaProgressReceivedImpl>
    implements _$$OtaProgressReceivedImplCopyWith<$Res> {
  __$$OtaProgressReceivedImplCopyWithImpl(_$OtaProgressReceivedImpl _value,
      $Res Function(_$OtaProgressReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progress = null,
  }) {
    return _then(_$OtaProgressReceivedImpl(
      null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as OtaProgress,
    ));
  }
}

/// @nodoc

class _$OtaProgressReceivedImpl implements OtaProgressReceived {
  const _$OtaProgressReceivedImpl(this.progress);

  @override
  final OtaProgress progress;

  @override
  String toString() {
    return 'OtaUpdateEvent.otaProgressReceived(progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtaProgressReceivedImpl &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, progress);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtaProgressReceivedImplCopyWith<_$OtaProgressReceivedImpl> get copyWith =>
      __$$OtaProgressReceivedImplCopyWithImpl<_$OtaProgressReceivedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return otaProgressReceived(progress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return otaProgressReceived?.call(progress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (otaProgressReceived != null) {
      return otaProgressReceived(progress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return otaProgressReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return otaProgressReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (otaProgressReceived != null) {
      return otaProgressReceived(this);
    }
    return orElse();
  }
}

abstract class OtaProgressReceived implements OtaUpdateEvent {
  const factory OtaProgressReceived(final OtaProgress progress) =
      _$OtaProgressReceivedImpl;

  OtaProgress get progress;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtaProgressReceivedImplCopyWith<_$OtaProgressReceivedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OtaErrorReceivedImplCopyWith<$Res> {
  factory _$$OtaErrorReceivedImplCopyWith(_$OtaErrorReceivedImpl value,
          $Res Function(_$OtaErrorReceivedImpl) then) =
      __$$OtaErrorReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothFailure failure});

  $BluetoothFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$OtaErrorReceivedImplCopyWithImpl<$Res>
    extends _$OtaUpdateEventCopyWithImpl<$Res, _$OtaErrorReceivedImpl>
    implements _$$OtaErrorReceivedImplCopyWith<$Res> {
  __$$OtaErrorReceivedImplCopyWithImpl(_$OtaErrorReceivedImpl _value,
      $Res Function(_$OtaErrorReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$OtaErrorReceivedImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as BluetoothFailure,
    ));
  }

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BluetoothFailureCopyWith<$Res> get failure {
    return $BluetoothFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$OtaErrorReceivedImpl implements OtaErrorReceived {
  const _$OtaErrorReceivedImpl(this.failure);

  @override
  final BluetoothFailure failure;

  @override
  String toString() {
    return 'OtaUpdateEvent.otaErrorReceived(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtaErrorReceivedImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtaErrorReceivedImplCopyWith<_$OtaErrorReceivedImpl> get copyWith =>
      __$$OtaErrorReceivedImplCopyWithImpl<_$OtaErrorReceivedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BluetoothDevice device) checkDeviceFirmware,
    required TResult Function(BluetoothDevice device) checkForUpdates,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        startOtaUpdate,
    required TResult Function(BluetoothDevice device) cancelOtaUpdate,
    required TResult Function() loadUpdateHistory,
    required TResult Function(BluetoothDevice device, Uint8List firmwareData)
        verifyFirmwareFile,
    required TResult Function(BluetoothDevice device) prepareDeviceForOta,
    required TResult Function(OtaProgress progress) otaProgressReceived,
    required TResult Function(BluetoothFailure failure) otaErrorReceived,
  }) {
    return otaErrorReceived(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult? Function(BluetoothDevice device)? checkForUpdates,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult? Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult? Function()? loadUpdateHistory,
    TResult? Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult? Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult? Function(OtaProgress progress)? otaProgressReceived,
    TResult? Function(BluetoothFailure failure)? otaErrorReceived,
  }) {
    return otaErrorReceived?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BluetoothDevice device)? checkDeviceFirmware,
    TResult Function(BluetoothDevice device)? checkForUpdates,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        startOtaUpdate,
    TResult Function(BluetoothDevice device)? cancelOtaUpdate,
    TResult Function()? loadUpdateHistory,
    TResult Function(BluetoothDevice device, Uint8List firmwareData)?
        verifyFirmwareFile,
    TResult Function(BluetoothDevice device)? prepareDeviceForOta,
    TResult Function(OtaProgress progress)? otaProgressReceived,
    TResult Function(BluetoothFailure failure)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (otaErrorReceived != null) {
      return otaErrorReceived(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckDeviceFirmware value) checkDeviceFirmware,
    required TResult Function(CheckForUpdates value) checkForUpdates,
    required TResult Function(StartOtaUpdate value) startOtaUpdate,
    required TResult Function(CancelOtaUpdate value) cancelOtaUpdate,
    required TResult Function(LoadUpdateHistory value) loadUpdateHistory,
    required TResult Function(VerifyFirmwareFile value) verifyFirmwareFile,
    required TResult Function(PrepareDeviceForOta value) prepareDeviceForOta,
    required TResult Function(OtaProgressReceived value) otaProgressReceived,
    required TResult Function(OtaErrorReceived value) otaErrorReceived,
  }) {
    return otaErrorReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult? Function(CheckForUpdates value)? checkForUpdates,
    TResult? Function(StartOtaUpdate value)? startOtaUpdate,
    TResult? Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult? Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult? Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult? Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult? Function(OtaProgressReceived value)? otaProgressReceived,
    TResult? Function(OtaErrorReceived value)? otaErrorReceived,
  }) {
    return otaErrorReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckDeviceFirmware value)? checkDeviceFirmware,
    TResult Function(CheckForUpdates value)? checkForUpdates,
    TResult Function(StartOtaUpdate value)? startOtaUpdate,
    TResult Function(CancelOtaUpdate value)? cancelOtaUpdate,
    TResult Function(LoadUpdateHistory value)? loadUpdateHistory,
    TResult Function(VerifyFirmwareFile value)? verifyFirmwareFile,
    TResult Function(PrepareDeviceForOta value)? prepareDeviceForOta,
    TResult Function(OtaProgressReceived value)? otaProgressReceived,
    TResult Function(OtaErrorReceived value)? otaErrorReceived,
    required TResult orElse(),
  }) {
    if (otaErrorReceived != null) {
      return otaErrorReceived(this);
    }
    return orElse();
  }
}

abstract class OtaErrorReceived implements OtaUpdateEvent {
  const factory OtaErrorReceived(final BluetoothFailure failure) =
      _$OtaErrorReceivedImpl;

  BluetoothFailure get failure;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtaErrorReceivedImplCopyWith<_$OtaErrorReceivedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$OtaUpdateState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtaUpdateStateCopyWith<$Res> {
  factory $OtaUpdateStateCopyWith(
          OtaUpdateState value, $Res Function(OtaUpdateState) then) =
      _$OtaUpdateStateCopyWithImpl<$Res, OtaUpdateState>;
}

/// @nodoc
class _$OtaUpdateStateCopyWithImpl<$Res, $Val extends OtaUpdateState>
    implements $OtaUpdateStateCopyWith<$Res> {
  _$OtaUpdateStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'OtaUpdateState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements OtaUpdateState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$CheckingFirmwareImplCopyWith<$Res> {
  factory _$$CheckingFirmwareImplCopyWith(_$CheckingFirmwareImpl value,
          $Res Function(_$CheckingFirmwareImpl) then) =
      __$$CheckingFirmwareImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckingFirmwareImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$CheckingFirmwareImpl>
    implements _$$CheckingFirmwareImplCopyWith<$Res> {
  __$$CheckingFirmwareImplCopyWithImpl(_$CheckingFirmwareImpl _value,
      $Res Function(_$CheckingFirmwareImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckingFirmwareImpl implements _CheckingFirmware {
  const _$CheckingFirmwareImpl();

  @override
  String toString() {
    return 'OtaUpdateState.checkingFirmware()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckingFirmwareImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return checkingFirmware();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return checkingFirmware?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (checkingFirmware != null) {
      return checkingFirmware();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return checkingFirmware(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return checkingFirmware?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (checkingFirmware != null) {
      return checkingFirmware(this);
    }
    return orElse();
  }
}

abstract class _CheckingFirmware implements OtaUpdateState {
  const factory _CheckingFirmware() = _$CheckingFirmwareImpl;
}

/// @nodoc
abstract class _$$FirmwareInfoLoadedImplCopyWith<$Res> {
  factory _$$FirmwareInfoLoadedImplCopyWith(_$FirmwareInfoLoadedImpl value,
          $Res Function(_$FirmwareInfoLoadedImpl) then) =
      __$$FirmwareInfoLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DeviceFirmwareInfo firmwareInfo});
}

/// @nodoc
class __$$FirmwareInfoLoadedImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$FirmwareInfoLoadedImpl>
    implements _$$FirmwareInfoLoadedImplCopyWith<$Res> {
  __$$FirmwareInfoLoadedImplCopyWithImpl(_$FirmwareInfoLoadedImpl _value,
      $Res Function(_$FirmwareInfoLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firmwareInfo = null,
  }) {
    return _then(_$FirmwareInfoLoadedImpl(
      null == firmwareInfo
          ? _value.firmwareInfo
          : firmwareInfo // ignore: cast_nullable_to_non_nullable
              as DeviceFirmwareInfo,
    ));
  }
}

/// @nodoc

class _$FirmwareInfoLoadedImpl implements _FirmwareInfoLoaded {
  const _$FirmwareInfoLoadedImpl(this.firmwareInfo);

  @override
  final DeviceFirmwareInfo firmwareInfo;

  @override
  String toString() {
    return 'OtaUpdateState.firmwareInfoLoaded(firmwareInfo: $firmwareInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FirmwareInfoLoadedImpl &&
            (identical(other.firmwareInfo, firmwareInfo) ||
                other.firmwareInfo == firmwareInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, firmwareInfo);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FirmwareInfoLoadedImplCopyWith<_$FirmwareInfoLoadedImpl> get copyWith =>
      __$$FirmwareInfoLoadedImplCopyWithImpl<_$FirmwareInfoLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return firmwareInfoLoaded(firmwareInfo);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return firmwareInfoLoaded?.call(firmwareInfo);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (firmwareInfoLoaded != null) {
      return firmwareInfoLoaded(firmwareInfo);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return firmwareInfoLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return firmwareInfoLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (firmwareInfoLoaded != null) {
      return firmwareInfoLoaded(this);
    }
    return orElse();
  }
}

abstract class _FirmwareInfoLoaded implements OtaUpdateState {
  const factory _FirmwareInfoLoaded(final DeviceFirmwareInfo firmwareInfo) =
      _$FirmwareInfoLoadedImpl;

  DeviceFirmwareInfo get firmwareInfo;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FirmwareInfoLoadedImplCopyWith<_$FirmwareInfoLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CheckingForUpdatesImplCopyWith<$Res> {
  factory _$$CheckingForUpdatesImplCopyWith(_$CheckingForUpdatesImpl value,
          $Res Function(_$CheckingForUpdatesImpl) then) =
      __$$CheckingForUpdatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckingForUpdatesImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$CheckingForUpdatesImpl>
    implements _$$CheckingForUpdatesImplCopyWith<$Res> {
  __$$CheckingForUpdatesImplCopyWithImpl(_$CheckingForUpdatesImpl _value,
      $Res Function(_$CheckingForUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckingForUpdatesImpl implements _CheckingForUpdates {
  const _$CheckingForUpdatesImpl();

  @override
  String toString() {
    return 'OtaUpdateState.checkingForUpdates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckingForUpdatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return checkingForUpdates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return checkingForUpdates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (checkingForUpdates != null) {
      return checkingForUpdates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return checkingForUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return checkingForUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (checkingForUpdates != null) {
      return checkingForUpdates(this);
    }
    return orElse();
  }
}

abstract class _CheckingForUpdates implements OtaUpdateState {
  const factory _CheckingForUpdates() = _$CheckingForUpdatesImpl;
}

/// @nodoc
abstract class _$$UpdateAvailableImplCopyWith<$Res> {
  factory _$$UpdateAvailableImplCopyWith(_$UpdateAvailableImpl value,
          $Res Function(_$UpdateAvailableImpl) then) =
      __$$UpdateAvailableImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FirmwareInfo updateInfo});
}

/// @nodoc
class __$$UpdateAvailableImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$UpdateAvailableImpl>
    implements _$$UpdateAvailableImplCopyWith<$Res> {
  __$$UpdateAvailableImplCopyWithImpl(
      _$UpdateAvailableImpl _value, $Res Function(_$UpdateAvailableImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updateInfo = null,
  }) {
    return _then(_$UpdateAvailableImpl(
      null == updateInfo
          ? _value.updateInfo
          : updateInfo // ignore: cast_nullable_to_non_nullable
              as FirmwareInfo,
    ));
  }
}

/// @nodoc

class _$UpdateAvailableImpl implements _UpdateAvailable {
  const _$UpdateAvailableImpl(this.updateInfo);

  @override
  final FirmwareInfo updateInfo;

  @override
  String toString() {
    return 'OtaUpdateState.updateAvailable(updateInfo: $updateInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAvailableImpl &&
            (identical(other.updateInfo, updateInfo) ||
                other.updateInfo == updateInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, updateInfo);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAvailableImplCopyWith<_$UpdateAvailableImpl> get copyWith =>
      __$$UpdateAvailableImplCopyWithImpl<_$UpdateAvailableImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return updateAvailable(updateInfo);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return updateAvailable?.call(updateInfo);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (updateAvailable != null) {
      return updateAvailable(updateInfo);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return updateAvailable(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return updateAvailable?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (updateAvailable != null) {
      return updateAvailable(this);
    }
    return orElse();
  }
}

abstract class _UpdateAvailable implements OtaUpdateState {
  const factory _UpdateAvailable(final FirmwareInfo updateInfo) =
      _$UpdateAvailableImpl;

  FirmwareInfo get updateInfo;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAvailableImplCopyWith<_$UpdateAvailableImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NoUpdatesAvailableImplCopyWith<$Res> {
  factory _$$NoUpdatesAvailableImplCopyWith(_$NoUpdatesAvailableImpl value,
          $Res Function(_$NoUpdatesAvailableImpl) then) =
      __$$NoUpdatesAvailableImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoUpdatesAvailableImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$NoUpdatesAvailableImpl>
    implements _$$NoUpdatesAvailableImplCopyWith<$Res> {
  __$$NoUpdatesAvailableImplCopyWithImpl(_$NoUpdatesAvailableImpl _value,
      $Res Function(_$NoUpdatesAvailableImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NoUpdatesAvailableImpl implements _NoUpdatesAvailable {
  const _$NoUpdatesAvailableImpl();

  @override
  String toString() {
    return 'OtaUpdateState.noUpdatesAvailable()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NoUpdatesAvailableImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return noUpdatesAvailable();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return noUpdatesAvailable?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (noUpdatesAvailable != null) {
      return noUpdatesAvailable();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return noUpdatesAvailable(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return noUpdatesAvailable?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (noUpdatesAvailable != null) {
      return noUpdatesAvailable(this);
    }
    return orElse();
  }
}

abstract class _NoUpdatesAvailable implements OtaUpdateState {
  const factory _NoUpdatesAvailable() = _$NoUpdatesAvailableImpl;
}

/// @nodoc
abstract class _$$VerifyingFirmwareImplCopyWith<$Res> {
  factory _$$VerifyingFirmwareImplCopyWith(_$VerifyingFirmwareImpl value,
          $Res Function(_$VerifyingFirmwareImpl) then) =
      __$$VerifyingFirmwareImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VerifyingFirmwareImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$VerifyingFirmwareImpl>
    implements _$$VerifyingFirmwareImplCopyWith<$Res> {
  __$$VerifyingFirmwareImplCopyWithImpl(_$VerifyingFirmwareImpl _value,
      $Res Function(_$VerifyingFirmwareImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VerifyingFirmwareImpl implements _VerifyingFirmware {
  const _$VerifyingFirmwareImpl();

  @override
  String toString() {
    return 'OtaUpdateState.verifyingFirmware()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$VerifyingFirmwareImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return verifyingFirmware();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return verifyingFirmware?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (verifyingFirmware != null) {
      return verifyingFirmware();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return verifyingFirmware(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return verifyingFirmware?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (verifyingFirmware != null) {
      return verifyingFirmware(this);
    }
    return orElse();
  }
}

abstract class _VerifyingFirmware implements OtaUpdateState {
  const factory _VerifyingFirmware() = _$VerifyingFirmwareImpl;
}

/// @nodoc
abstract class _$$FirmwareVerifiedImplCopyWith<$Res> {
  factory _$$FirmwareVerifiedImplCopyWith(_$FirmwareVerifiedImpl value,
          $Res Function(_$FirmwareVerifiedImpl) then) =
      __$$FirmwareVerifiedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FirmwareVerifiedImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$FirmwareVerifiedImpl>
    implements _$$FirmwareVerifiedImplCopyWith<$Res> {
  __$$FirmwareVerifiedImplCopyWithImpl(_$FirmwareVerifiedImpl _value,
      $Res Function(_$FirmwareVerifiedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FirmwareVerifiedImpl implements _FirmwareVerified {
  const _$FirmwareVerifiedImpl();

  @override
  String toString() {
    return 'OtaUpdateState.firmwareVerified()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FirmwareVerifiedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return firmwareVerified();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return firmwareVerified?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (firmwareVerified != null) {
      return firmwareVerified();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return firmwareVerified(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return firmwareVerified?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (firmwareVerified != null) {
      return firmwareVerified(this);
    }
    return orElse();
  }
}

abstract class _FirmwareVerified implements OtaUpdateState {
  const factory _FirmwareVerified() = _$FirmwareVerifiedImpl;
}

/// @nodoc
abstract class _$$PreparingDeviceImplCopyWith<$Res> {
  factory _$$PreparingDeviceImplCopyWith(_$PreparingDeviceImpl value,
          $Res Function(_$PreparingDeviceImpl) then) =
      __$$PreparingDeviceImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PreparingDeviceImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$PreparingDeviceImpl>
    implements _$$PreparingDeviceImplCopyWith<$Res> {
  __$$PreparingDeviceImplCopyWithImpl(
      _$PreparingDeviceImpl _value, $Res Function(_$PreparingDeviceImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PreparingDeviceImpl implements _PreparingDevice {
  const _$PreparingDeviceImpl();

  @override
  String toString() {
    return 'OtaUpdateState.preparingDevice()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PreparingDeviceImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return preparingDevice();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return preparingDevice?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (preparingDevice != null) {
      return preparingDevice();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return preparingDevice(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return preparingDevice?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (preparingDevice != null) {
      return preparingDevice(this);
    }
    return orElse();
  }
}

abstract class _PreparingDevice implements OtaUpdateState {
  const factory _PreparingDevice() = _$PreparingDeviceImpl;
}

/// @nodoc
abstract class _$$DevicePreparedImplCopyWith<$Res> {
  factory _$$DevicePreparedImplCopyWith(_$DevicePreparedImpl value,
          $Res Function(_$DevicePreparedImpl) then) =
      __$$DevicePreparedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DevicePreparedImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$DevicePreparedImpl>
    implements _$$DevicePreparedImplCopyWith<$Res> {
  __$$DevicePreparedImplCopyWithImpl(
      _$DevicePreparedImpl _value, $Res Function(_$DevicePreparedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DevicePreparedImpl implements _DevicePrepared {
  const _$DevicePreparedImpl();

  @override
  String toString() {
    return 'OtaUpdateState.devicePrepared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DevicePreparedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return devicePrepared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return devicePrepared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (devicePrepared != null) {
      return devicePrepared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return devicePrepared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return devicePrepared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (devicePrepared != null) {
      return devicePrepared(this);
    }
    return orElse();
  }
}

abstract class _DevicePrepared implements OtaUpdateState {
  const factory _DevicePrepared() = _$DevicePreparedImpl;
}

/// @nodoc
abstract class _$$PreparingUpdateImplCopyWith<$Res> {
  factory _$$PreparingUpdateImplCopyWith(_$PreparingUpdateImpl value,
          $Res Function(_$PreparingUpdateImpl) then) =
      __$$PreparingUpdateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PreparingUpdateImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$PreparingUpdateImpl>
    implements _$$PreparingUpdateImplCopyWith<$Res> {
  __$$PreparingUpdateImplCopyWithImpl(
      _$PreparingUpdateImpl _value, $Res Function(_$PreparingUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PreparingUpdateImpl implements _PreparingUpdate {
  const _$PreparingUpdateImpl();

  @override
  String toString() {
    return 'OtaUpdateState.preparingUpdate()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PreparingUpdateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return preparingUpdate();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return preparingUpdate?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (preparingUpdate != null) {
      return preparingUpdate();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return preparingUpdate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return preparingUpdate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (preparingUpdate != null) {
      return preparingUpdate(this);
    }
    return orElse();
  }
}

abstract class _PreparingUpdate implements OtaUpdateState {
  const factory _PreparingUpdate() = _$PreparingUpdateImpl;
}

/// @nodoc
abstract class _$$UpdateInProgressImplCopyWith<$Res> {
  factory _$$UpdateInProgressImplCopyWith(_$UpdateInProgressImpl value,
          $Res Function(_$UpdateInProgressImpl) then) =
      __$$UpdateInProgressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({OtaProgress progress});
}

/// @nodoc
class __$$UpdateInProgressImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$UpdateInProgressImpl>
    implements _$$UpdateInProgressImplCopyWith<$Res> {
  __$$UpdateInProgressImplCopyWithImpl(_$UpdateInProgressImpl _value,
      $Res Function(_$UpdateInProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progress = null,
  }) {
    return _then(_$UpdateInProgressImpl(
      null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as OtaProgress,
    ));
  }
}

/// @nodoc

class _$UpdateInProgressImpl implements _UpdateInProgress {
  const _$UpdateInProgressImpl(this.progress);

  @override
  final OtaProgress progress;

  @override
  String toString() {
    return 'OtaUpdateState.updateInProgress(progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateInProgressImpl &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, progress);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateInProgressImplCopyWith<_$UpdateInProgressImpl> get copyWith =>
      __$$UpdateInProgressImplCopyWithImpl<_$UpdateInProgressImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return updateInProgress(progress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return updateInProgress?.call(progress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (updateInProgress != null) {
      return updateInProgress(progress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return updateInProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return updateInProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (updateInProgress != null) {
      return updateInProgress(this);
    }
    return orElse();
  }
}

abstract class _UpdateInProgress implements OtaUpdateState {
  const factory _UpdateInProgress(final OtaProgress progress) =
      _$UpdateInProgressImpl;

  OtaProgress get progress;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateInProgressImplCopyWith<_$UpdateInProgressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateCompletedImplCopyWith<$Res> {
  factory _$$UpdateCompletedImplCopyWith(_$UpdateCompletedImpl value,
          $Res Function(_$UpdateCompletedImpl) then) =
      __$$UpdateCompletedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateCompletedImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$UpdateCompletedImpl>
    implements _$$UpdateCompletedImplCopyWith<$Res> {
  __$$UpdateCompletedImplCopyWithImpl(
      _$UpdateCompletedImpl _value, $Res Function(_$UpdateCompletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateCompletedImpl implements _UpdateCompleted {
  const _$UpdateCompletedImpl();

  @override
  String toString() {
    return 'OtaUpdateState.updateCompleted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdateCompletedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return updateCompleted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return updateCompleted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (updateCompleted != null) {
      return updateCompleted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return updateCompleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return updateCompleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (updateCompleted != null) {
      return updateCompleted(this);
    }
    return orElse();
  }
}

abstract class _UpdateCompleted implements OtaUpdateState {
  const factory _UpdateCompleted() = _$UpdateCompletedImpl;
}

/// @nodoc
abstract class _$$CancellingUpdateImplCopyWith<$Res> {
  factory _$$CancellingUpdateImplCopyWith(_$CancellingUpdateImpl value,
          $Res Function(_$CancellingUpdateImpl) then) =
      __$$CancellingUpdateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CancellingUpdateImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$CancellingUpdateImpl>
    implements _$$CancellingUpdateImplCopyWith<$Res> {
  __$$CancellingUpdateImplCopyWithImpl(_$CancellingUpdateImpl _value,
      $Res Function(_$CancellingUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CancellingUpdateImpl implements _CancellingUpdate {
  const _$CancellingUpdateImpl();

  @override
  String toString() {
    return 'OtaUpdateState.cancellingUpdate()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CancellingUpdateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return cancellingUpdate();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return cancellingUpdate?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (cancellingUpdate != null) {
      return cancellingUpdate();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return cancellingUpdate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return cancellingUpdate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (cancellingUpdate != null) {
      return cancellingUpdate(this);
    }
    return orElse();
  }
}

abstract class _CancellingUpdate implements OtaUpdateState {
  const factory _CancellingUpdate() = _$CancellingUpdateImpl;
}

/// @nodoc
abstract class _$$UpdateCancelledImplCopyWith<$Res> {
  factory _$$UpdateCancelledImplCopyWith(_$UpdateCancelledImpl value,
          $Res Function(_$UpdateCancelledImpl) then) =
      __$$UpdateCancelledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateCancelledImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$UpdateCancelledImpl>
    implements _$$UpdateCancelledImplCopyWith<$Res> {
  __$$UpdateCancelledImplCopyWithImpl(
      _$UpdateCancelledImpl _value, $Res Function(_$UpdateCancelledImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateCancelledImpl implements _UpdateCancelled {
  const _$UpdateCancelledImpl();

  @override
  String toString() {
    return 'OtaUpdateState.updateCancelled()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdateCancelledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return updateCancelled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return updateCancelled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (updateCancelled != null) {
      return updateCancelled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return updateCancelled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return updateCancelled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (updateCancelled != null) {
      return updateCancelled(this);
    }
    return orElse();
  }
}

abstract class _UpdateCancelled implements OtaUpdateState {
  const factory _UpdateCancelled() = _$UpdateCancelledImpl;
}

/// @nodoc
abstract class _$$LoadingHistoryImplCopyWith<$Res> {
  factory _$$LoadingHistoryImplCopyWith(_$LoadingHistoryImpl value,
          $Res Function(_$LoadingHistoryImpl) then) =
      __$$LoadingHistoryImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingHistoryImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$LoadingHistoryImpl>
    implements _$$LoadingHistoryImplCopyWith<$Res> {
  __$$LoadingHistoryImplCopyWithImpl(
      _$LoadingHistoryImpl _value, $Res Function(_$LoadingHistoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingHistoryImpl implements _LoadingHistory {
  const _$LoadingHistoryImpl();

  @override
  String toString() {
    return 'OtaUpdateState.loadingHistory()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingHistoryImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return loadingHistory();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return loadingHistory?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (loadingHistory != null) {
      return loadingHistory();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return loadingHistory(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return loadingHistory?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loadingHistory != null) {
      return loadingHistory(this);
    }
    return orElse();
  }
}

abstract class _LoadingHistory implements OtaUpdateState {
  const factory _LoadingHistory() = _$LoadingHistoryImpl;
}

/// @nodoc
abstract class _$$HistoryLoadedImplCopyWith<$Res> {
  factory _$$HistoryLoadedImplCopyWith(
          _$HistoryLoadedImpl value, $Res Function(_$HistoryLoadedImpl) then) =
      __$$HistoryLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<OtaUpdateRecord> history});
}

/// @nodoc
class __$$HistoryLoadedImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$HistoryLoadedImpl>
    implements _$$HistoryLoadedImplCopyWith<$Res> {
  __$$HistoryLoadedImplCopyWithImpl(
      _$HistoryLoadedImpl _value, $Res Function(_$HistoryLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? history = null,
  }) {
    return _then(_$HistoryLoadedImpl(
      null == history
          ? _value._history
          : history // ignore: cast_nullable_to_non_nullable
              as List<OtaUpdateRecord>,
    ));
  }
}

/// @nodoc

class _$HistoryLoadedImpl implements _HistoryLoaded {
  const _$HistoryLoadedImpl(final List<OtaUpdateRecord> history)
      : _history = history;

  final List<OtaUpdateRecord> _history;
  @override
  List<OtaUpdateRecord> get history {
    if (_history is EqualUnmodifiableListView) return _history;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_history);
  }

  @override
  String toString() {
    return 'OtaUpdateState.historyLoaded(history: $history)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryLoadedImpl &&
            const DeepCollectionEquality().equals(other._history, _history));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_history));

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryLoadedImplCopyWith<_$HistoryLoadedImpl> get copyWith =>
      __$$HistoryLoadedImplCopyWithImpl<_$HistoryLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return historyLoaded(history);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return historyLoaded?.call(history);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (historyLoaded != null) {
      return historyLoaded(history);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return historyLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return historyLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (historyLoaded != null) {
      return historyLoaded(this);
    }
    return orElse();
  }
}

abstract class _HistoryLoaded implements OtaUpdateState {
  const factory _HistoryLoaded(final List<OtaUpdateRecord> history) =
      _$HistoryLoadedImpl;

  List<OtaUpdateRecord> get history;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HistoryLoadedImplCopyWith<_$HistoryLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BluetoothFailure failure});

  $BluetoothFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$OtaUpdateStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$ErrorImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as BluetoothFailure,
    ));
  }

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BluetoothFailureCopyWith<$Res> get failure {
    return $BluetoothFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.failure);

  @override
  final BluetoothFailure failure;

  @override
  String toString() {
    return 'OtaUpdateState.error(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() checkingFirmware,
    required TResult Function(DeviceFirmwareInfo firmwareInfo)
        firmwareInfoLoaded,
    required TResult Function() checkingForUpdates,
    required TResult Function(FirmwareInfo updateInfo) updateAvailable,
    required TResult Function() noUpdatesAvailable,
    required TResult Function() verifyingFirmware,
    required TResult Function() firmwareVerified,
    required TResult Function() preparingDevice,
    required TResult Function() devicePrepared,
    required TResult Function() preparingUpdate,
    required TResult Function(OtaProgress progress) updateInProgress,
    required TResult Function() updateCompleted,
    required TResult Function() cancellingUpdate,
    required TResult Function() updateCancelled,
    required TResult Function() loadingHistory,
    required TResult Function(List<OtaUpdateRecord> history) historyLoaded,
    required TResult Function(BluetoothFailure failure) error,
  }) {
    return error(failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? checkingFirmware,
    TResult? Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult? Function()? checkingForUpdates,
    TResult? Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult? Function()? noUpdatesAvailable,
    TResult? Function()? verifyingFirmware,
    TResult? Function()? firmwareVerified,
    TResult? Function()? preparingDevice,
    TResult? Function()? devicePrepared,
    TResult? Function()? preparingUpdate,
    TResult? Function(OtaProgress progress)? updateInProgress,
    TResult? Function()? updateCompleted,
    TResult? Function()? cancellingUpdate,
    TResult? Function()? updateCancelled,
    TResult? Function()? loadingHistory,
    TResult? Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult? Function(BluetoothFailure failure)? error,
  }) {
    return error?.call(failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? checkingFirmware,
    TResult Function(DeviceFirmwareInfo firmwareInfo)? firmwareInfoLoaded,
    TResult Function()? checkingForUpdates,
    TResult Function(FirmwareInfo updateInfo)? updateAvailable,
    TResult Function()? noUpdatesAvailable,
    TResult Function()? verifyingFirmware,
    TResult Function()? firmwareVerified,
    TResult Function()? preparingDevice,
    TResult Function()? devicePrepared,
    TResult Function()? preparingUpdate,
    TResult Function(OtaProgress progress)? updateInProgress,
    TResult Function()? updateCompleted,
    TResult Function()? cancellingUpdate,
    TResult Function()? updateCancelled,
    TResult Function()? loadingHistory,
    TResult Function(List<OtaUpdateRecord> history)? historyLoaded,
    TResult Function(BluetoothFailure failure)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_CheckingFirmware value) checkingFirmware,
    required TResult Function(_FirmwareInfoLoaded value) firmwareInfoLoaded,
    required TResult Function(_CheckingForUpdates value) checkingForUpdates,
    required TResult Function(_UpdateAvailable value) updateAvailable,
    required TResult Function(_NoUpdatesAvailable value) noUpdatesAvailable,
    required TResult Function(_VerifyingFirmware value) verifyingFirmware,
    required TResult Function(_FirmwareVerified value) firmwareVerified,
    required TResult Function(_PreparingDevice value) preparingDevice,
    required TResult Function(_DevicePrepared value) devicePrepared,
    required TResult Function(_PreparingUpdate value) preparingUpdate,
    required TResult Function(_UpdateInProgress value) updateInProgress,
    required TResult Function(_UpdateCompleted value) updateCompleted,
    required TResult Function(_CancellingUpdate value) cancellingUpdate,
    required TResult Function(_UpdateCancelled value) updateCancelled,
    required TResult Function(_LoadingHistory value) loadingHistory,
    required TResult Function(_HistoryLoaded value) historyLoaded,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_CheckingFirmware value)? checkingFirmware,
    TResult? Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult? Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult? Function(_UpdateAvailable value)? updateAvailable,
    TResult? Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult? Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult? Function(_FirmwareVerified value)? firmwareVerified,
    TResult? Function(_PreparingDevice value)? preparingDevice,
    TResult? Function(_DevicePrepared value)? devicePrepared,
    TResult? Function(_PreparingUpdate value)? preparingUpdate,
    TResult? Function(_UpdateInProgress value)? updateInProgress,
    TResult? Function(_UpdateCompleted value)? updateCompleted,
    TResult? Function(_CancellingUpdate value)? cancellingUpdate,
    TResult? Function(_UpdateCancelled value)? updateCancelled,
    TResult? Function(_LoadingHistory value)? loadingHistory,
    TResult? Function(_HistoryLoaded value)? historyLoaded,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_CheckingFirmware value)? checkingFirmware,
    TResult Function(_FirmwareInfoLoaded value)? firmwareInfoLoaded,
    TResult Function(_CheckingForUpdates value)? checkingForUpdates,
    TResult Function(_UpdateAvailable value)? updateAvailable,
    TResult Function(_NoUpdatesAvailable value)? noUpdatesAvailable,
    TResult Function(_VerifyingFirmware value)? verifyingFirmware,
    TResult Function(_FirmwareVerified value)? firmwareVerified,
    TResult Function(_PreparingDevice value)? preparingDevice,
    TResult Function(_DevicePrepared value)? devicePrepared,
    TResult Function(_PreparingUpdate value)? preparingUpdate,
    TResult Function(_UpdateInProgress value)? updateInProgress,
    TResult Function(_UpdateCompleted value)? updateCompleted,
    TResult Function(_CancellingUpdate value)? cancellingUpdate,
    TResult Function(_UpdateCancelled value)? updateCancelled,
    TResult Function(_LoadingHistory value)? loadingHistory,
    TResult Function(_HistoryLoaded value)? historyLoaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements OtaUpdateState {
  const factory _Error(final BluetoothFailure failure) = _$ErrorImpl;

  BluetoothFailure get failure;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
