import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:flutter/animation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remote/domain/model/battery_level_model.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/domain/model/device_info_model.dart';
import 'package:remote/domain/model/device_model.dart';
import 'package:remote/domain/model/heat_level.dart';
import 'package:remote/domain/model/tens_level_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'bluetooth_constants.dart';

/// This class implements the IBluetoothFacade interface
@LazySingleton(as: IBluetoothFacade)
class BluetoothServiceRepository implements IBluetoothFacade {
  final String _deviceName = 'Juno Lilly';
  late BluetoothDevice bluetoothDevice;
  DeviceModel _deviceModel = DeviceModel(
    deviceInfo: DeviceInfoModel(
        deviceName: 'Juno Lilly',
        deviceAddress: '',
        deviceType: 'Juno Lilly',
        deviceId: '',
        batteryLevel: BatteryLevelModel(batteryLevel: 0)),
    isDeviceOn: true,
    heatLevel: HeatLevelModel(selectedHeatLevel: 0, actualHeatLevel: 0),
    tensLevel:
        TensLevelModel(selectedTensLevel: 0, actualTensLevel: 0, mode: 1),
    recentCommands: [],
  );
  bool _shouldStopScan = false;
  late List<BluetoothService> bluetoothService;
  List<BluetoothDevice> bluetoothDevices = [];
  List<DeviceModel> savedDevices = [];

  final _heatLevelController =
      StreamController<Either<BluetoothFailure, HeatLevelModel>>.broadcast();
  final _tensLevelController =
      StreamController<Either<BluetoothFailure, HeatLevelModel>>.broadcast();
  // Method to get paired devices
  @override
  Future<Either<BluetoothFailure, List<BluetoothDevice>>> getPairedDevices() {
    throw UnimplementedError();
  }

  // Method to pair a device
  @override
  Future<Either<BluetoothFailure, BluetoothDevice>> pairDevice(
      String device, bool initial) async {
    int retryCount = 0;
    const int maxRetries = 1;
    _shouldStopScan = initial;
    while (retryCount < maxRetries) {
     debugPrint('Retrying to connect to device: $retryCount');
      try {
        bluetoothDevice = bluetoothDevices.isEmpty
            ? BluetoothDevice.fromId(device)
            : bluetoothDevices.firstWhere(
                (element) =>
                    element.remoteId.str.toLowerCase() == device.toLowerCase(),
              );

        final isConnected = bluetoothDevice.isConnected;
        if (isConnected) {
          bluetoothService = await bluetoothDevice.discoverServices();
          return Right(bluetoothDevice);
        } else {
          try {
            await bluetoothDevice.connect(mtu: null);
            bluetoothService = await bluetoothDevice.discoverServices();
            await saveDevice(bluetoothDevice);
            bluetoothService.forEach((service) {
             debugPrint('Service UUID: ${service.uuid}');
              service.characteristics.forEach((characteristic) {
               debugPrint('Characteristic UUID: ${characteristic.uuid}');
              });
            });
            return Right(bluetoothDevice);
            //print characteristics
          } on TimeoutException {
            retryCount++;
            if (retryCount >= maxRetries) {
              return const Left(BluetoothFailure.searchTimeout());
            }
          } on FlutterBluePlusException {
            retryCount++;
            if (retryCount >= maxRetries) {
              return const Left(BluetoothFailure.deviceConnectionFailed());
            }
          }
        }
      } catch (e) {
        retryCount++;
        if (retryCount >= maxRetries) {
          return const Left(BluetoothFailure.deviceConnectionFailed());
        }
      }
    }
    return const Left(BluetoothFailure.deviceConnectionFailed());
  }

  // Method to unpair a device
  @override
  Future<Either<BluetoothFailure, void>> unpairDevice(BluetoothDevice device) {
    throw UnimplementedError();
  }

  // Method to check if Bluetooth is on
  @override
  Stream<bool> isBluetoothOn() {
    FlutterBluePlus.setLogLevel(LogLevel.none, color: false);
    return FlutterBluePlus.adapterState
        .map((state) => state == BluetoothAdapterState.on);
  }

  // Method to search for devices
  @override
  Future<Either<BluetoothFailure, List<BluetoothDevice?>?>>
      searchForDevices() async {
    try {
     debugPrint('Searching for devices..........................');
      late List<ScanResult> scanResult;
      bluetoothDevices.clear();
      final subscription = FlutterBluePlus.scanResults.listen((results) {
        scanResult = results;
      });

      await FlutterBluePlus.startScan(
          // withKeywords: ['Lily','lily'],
          timeout: const Duration(seconds: 4));
      FlutterBluePlus.cancelWhenScanComplete(subscription);
      await FlutterBluePlus.isScanning.where((val) => val == false).first;
      for (ScanResult result in scanResult) {
       debugPrint(result.device.platformName);
        // Include all devices in scan results, even those with empty platform names
        // This is important for saved device detection
        bluetoothDevices.addOrUpdate(result.device);
      }

      return Right(bluetoothDevices.toList());
    } catch (e) {
      return const Left(BluetoothFailure.searchTimeout());
    }
  }

  // Method to listen to a device
  @override
  Stream<BluetoothConnectionState> listenToDevice(BluetoothDevice device) {
    return device.connectionState.asBroadcastStream();
  }

  //function to print all services and characteristics of a device
  @override
  Future<Either<BluetoothFailure, List<BluetoothService>>> getDeviceServices(
      BluetoothDevice device) async {
    try {
      bluetoothService = await device.discoverServices();
      bluetoothService.forEach((service) {
       debugPrint('Service UUID: ${service.uuid}');
        service.characteristics.forEach((characteristic) {
         debugPrint('Characteristic UUID: ${characteristic.uuid}');
        });
      });
      return Right(bluetoothService);
    } catch (e) {
      return const Left(BluetoothFailure.getCommandFailed());
    }
  }

  // Method to listen to device indications
  @override
  Stream<Either<BluetoothFailure, List<int>>> listenToIndication(
      Commands command) async* {
    try {
     debugPrint('Setting up indication listener for: ${command.commandType}');
      final String serviceUuid;
      final String characteristicUuid;
      getDeviceServices(bluetoothDevice);

      // Determine which service and characteristic to use based on command type
      if (command.commandType == 'measuredIntensity') {
        serviceUuid = BluetoothConstants.tensControlServiceUUID;
        characteristicUuid = BluetoothConstants.measuredIntensityUUID;
      } else if (command.commandType == 'currentTemp') {
        serviceUuid = BluetoothConstants.temperatureControlServiceUUID;
        characteristicUuid = BluetoothConstants.currentTemperatureUUID;
      } else if (command.commandType == 'activeTens') {
        serviceUuid = BluetoothConstants.tensControlServiceUUID;
        characteristicUuid = BluetoothConstants.activeTensUUID;
      } else if (command.commandType == 'setTensMode') {
        serviceUuid = BluetoothConstants.tensControlServiceUUID;
        characteristicUuid = BluetoothConstants.tensModeUUID;
      } else {
       debugPrint('Unknown indication type: ${command.commandType}');
        yield const Left(BluetoothFailure.getCommandFailed());
        return;
      }

      final service = await _getService(serviceUuid);
      final characteristic =
          await _getCharacteristic(service, characteristicUuid);

      // Enable notifications (indications)
      await characteristic.setNotifyValue(true);
     debugPrint('Notifications enabled for ${command.commandType} characteristic');

      // Listen to characteristic value changes
      yield* characteristic.lastValueStream
          .map<Either<BluetoothFailure, List<int>>>((value) {
        if (value.isNotEmpty) {
          if (command.commandType == 'tensIndication') {
           debugPrint('Received TENS indication value: $value');
          } else {
            //debugPrint('Received HEAT indication value: $value');
          }
          return Right(value);
        } else {
         debugPrint('Empty indication value received');
          return const Left(BluetoothFailure.getCommandFailed());
        }
      }).handleError((error) {
        print('Error in indication stream: $error');
        return const Left(BluetoothFailure.getCommandFailed());
      });
    } catch (e) {
      print('Exception in listenToIndication: $e');
      yield const Left(BluetoothFailure.getCommandFailed());
    }
  }

  // Method to check if a device is connected
  @override
  Future<Either<BluetoothFailure, BluetoothDevice?>> deviceConnected() async {
    try {
      var connectedDevices = Platform.isIOS
          ? await FlutterBluePlus.systemDevices
          : await FlutterBluePlus.bondedDevices;
      connectedDevices.addAll(FlutterBluePlus.connectedDevices);

      print(connectedDevices);

      if (connectedDevices.isNotEmpty) {
        BluetoothDevice? device = connectedDevices.firstWhere(
          (element) => element.platformName.toLowerCase().contains(_deviceName),
          orElse: () => BluetoothDevice(remoteId: const DeviceIdentifier('')),
        );

        if (device.platformName == 'Lily') {
          if (device.isConnected) {
            return Right(device);
          } else {
            try {
              pairDevice(device.remoteId.str, false);
              return Right(device);
            } catch (e) {
              return const Left(BluetoothFailure.deviceConnectionFailed());
            }
          }
        } else {
          return const Left(BluetoothFailure.deviceConnectionFailed());
        }
      } else {
        return const Left(BluetoothFailure.noDevicesFound());
      }
    } catch (e) {
      print(e);
      return const Left(BluetoothFailure.unexpected());
    }
  }

  @override
  Future<Either<BluetoothFailure, bool>> saveDevice(
      BluetoothDevice device) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final savedDevicesJson = prefs.getStringList('savedDevices') ?? [];

      // Check if the device is already saved
      for (var deviceJson in savedDevicesJson) {
        final savedDevice = DeviceModel.fromJson(jsonDecode(deviceJson));
        if (savedDevice.deviceInfo.deviceId == device.remoteId.str) {
          // Update lastConnected time
          savedDevicesJson.remove(deviceJson);
          final updatedDevice =
              savedDevice.copyWith(lastConnected: DateTime.now());
          savedDevicesJson.add(jsonEncode(updatedDevice.toJson()));
          await prefs.setStringList('savedDevices', savedDevicesJson);

          // Save as recent device
          await prefs.setString(
              'recentDevice', jsonEncode(updatedDevice.toJson()));

          return const Right(false); // Device already saved
        }
      }

      final deviceModel = DeviceModel(
        deviceInfo: DeviceInfoModel(
            deviceName: device.platformName,
            deviceAddress: device.remoteId.str,
            deviceType: device.platformName,
            deviceId: device.remoteId.str,
            batteryLevel: BatteryLevelModel(batteryLevel: 0)),
        isDeviceOn: true, // Assuming the device is on when saving
        // Assuming full battery
        heatLevel: HeatLevelModel(selectedHeatLevel: 0, actualHeatLevel: 0),
        tensLevel:
            TensLevelModel(selectedTensLevel: 0, actualTensLevel: 0, mode: 1),
        recentCommands: [],
        lastConnected: DateTime.now(),
      );

      final deviceJson = jsonEncode(deviceModel.toJson());
      savedDevicesJson.add(deviceJson);

      await prefs.setStringList('savedDevices', savedDevicesJson);

      // Save as recent device
      await prefs.setString('recentDevice', deviceJson);

      return const Right(true);
    } catch (e) {
      return const Left(BluetoothFailure.saveDeviceFailed());
    }
  }

  // Method to stop background scanning
  @override
  void stopBackgroundScanning() {
    _shouldStopScan = true;
  }

  // Method to get a saved device
  @override
  Stream<Either<BluetoothFailure, List<DeviceModel>>>
      getSavedDevicesList() async* {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final savedDevicesJson = prefs.getStringList('savedDevices') ?? [];
      final savedDevices = savedDevicesJson.map((deviceJson) {
        return DeviceModel.fromJson(jsonDecode(deviceJson));
      }).toList();

      // Return saved devices first
      yield Right(savedDevices);
      _shouldStopScan = false;
      while (!_shouldStopScan) {
        print('Scanning for devices');
        final result = await searchForDevices();
        result.mapBoth(
          onLeft: (failure) => print('Error scanning for devices: $failure'),
          onRight: (scannedDevices) {
            print('Found ${scannedDevices?.length ?? 0} devices in scan');
            for (var savedDevice in savedDevices) {
              print(
                  'Checking saved device: ${savedDevice.deviceInfo.deviceId}');
              bool wasFound = scannedDevices!.any((device) =>
                  device!.remoteId.str == savedDevice.deviceInfo.deviceId);
              savedDevice.isDeviceReady = wasFound;
              print(
                  'Device ${savedDevice.deviceInfo.deviceId} availability: $wasFound');
            }
          },
        );
        yield Right(savedDevices);
        // Do not remove this delay
        await Future.delayed(const Duration(seconds: 5));
      }
    } catch (e) {
      yield const Left(BluetoothFailure.getSavedDeviceFailed());
    }
  }

  // // Method to connect to saved devices
  // @override
  // Future<Either<BluetoothFailure, BluetoothDevice>> connectToSavedDevices(
  //     String? savedDeviceIds) async {
  //   try {
  //     late List<ScanResult> scanResult;
  //     final subscription = FlutterBluePlus.scanResults.listen((results) {
  //       scanResult = results;
  //     });
  //     await FlutterBluePlus.startScan(
  //         withKeywords: [_deviceName], timeout: const Duration(seconds: 4));
  //     await Future.delayed(const Duration(seconds: 4));
  //     FlutterBluePlus.cancelWhenScanComplete(subscription);
  //     for (ScanResult result in scanResult) {
  //       if (savedDeviceIds == result.device.remoteId.str) {
  //         try {
  //           await result.device.connect(
  //               timeout: const Duration(seconds: 60),
  //               autoConnect: false,
  //               mtu: null);
  //           await Future.delayed(const Duration(seconds: 10));
  //           result.device.connectionState.listen((event) {});
  //           return Right(result.device);
  //         } catch (e) {
  //           return const Left(BluetoothFailure.deviceConnectionFailed());
  //         }
  //       }
  //     }
  //     return const Left(BluetoothFailure.noDevicesFound());
  //   } catch (e) {
  //     return const Left(BluetoothFailure.noDevicesFound());
  //   }
  // }

  // Method to get information from device every 2 seconds
  @override
  Stream<Either<BluetoothFailure, DeviceModel>> getDeviceInformation() {
    return Stream.periodic(const Duration(milliseconds: 200), (i) => '')
        .asyncMap((device) async {
      try {
        // bluetoothService.forEach((service) {
        //   if (service.uuid == Guid('00002a46-0000-1000-8000-00805f9b34f9')) {
        //     service.characteristics.forEach((characteristic) {
        //       if (characteristic.uuid == Guid('00002a47-0000-1000-8000-00805f9b34f8')) {
        //         //readevry 500ms
        //
        //         Timer.periodic(Duration(milliseconds: 200), (timer) async {
        //           final value = await characteristic.read();
        //           _deviceModel.tensLevel?.copyWith(actualTensLevel: value[0]);
        //
        //         });
        //       }
        //     });
        //   }
        // });
        return Right(_deviceModel);
      } catch (e) {
        return Left(BluetoothFailure.getCommandFailed());
      }
    });
  }

  //method to get current heat level and target heat level from device

  /// **Read characteristic once**
  @override
  Future<Either<BluetoothFailure, List<int>>> readDeviceCharacteristics(
      Commands command) async {
    try {
      final serviceUuid = _mapActionToServiceUuid(command.commandType);
      final characteristicUuid =
          _mapActionToCharacteristicUuid(command.commandType);

      final service = await _getService(serviceUuid);
      final characteristic =
          await _getCharacteristic(service, characteristicUuid);

      final value = await characteristic.read();
      return Right(value); // Assuming value is a single byte
    } catch (e) {
      return const Left(BluetoothFailure.getCommandFailed());
    }
  }

  Future<Either<BluetoothFailure, Unit>> sendCommand(Commands command) async {
    try {
      if (bluetoothDevice.isConnected) {
        // print(
        //     'Sending command: ${command.commandType} with +++++++++++++++++++++++++++++++++   value: ${command.value}');
        final serviceUuid = _mapActionToServiceUuid(command.commandType);
        final characteristicUuid =
            _mapActionToCharacteristicUuid(command.commandType);

        // Get the service first
        final service = await _getService(serviceUuid);
        // Then get the characteristic within the service
        final characteristic =
            await _getCharacteristic(service, characteristicUuid);
        // If there's a value to write, pass it to the characteristic

        await characteristic.write([command.value], withoutResponse: false);
      }
      return const Right(unit);
    } catch (e) {
      print("************************************");
      print(e.toString());
      return const Left(BluetoothFailure.sendCommandFailed());
    }
  }

  // Map action to its corresponding service UUID
  String _mapActionToServiceUuid(String action) {
    switch (action) {
      case 'powerOff':
      case 'startTherapy':
      case 'pauseTherapy':
      case 'therapyState':
        return BluetoothConstants.deviceControlServiceUUID;
      case 'increaseHeat':
      case 'decreaseHeat':
      case 'targetTemp':
      case 'currentTemp':
      case 'heatStatus':
      case 'heatingElementControl':
        return BluetoothConstants.temperatureControlServiceUUID;
      case 'increaseTens':
      case 'decreaseTens':
      case 'targetTens':
      case 'currentTens':
      case 'setTensMode':
      case 'measuredIntensity':
      case 'tensStatus':
      case 'activeTens':
      case 'tensControl':
        return BluetoothConstants.tensControlServiceUUID;
      default:
        throw ArgumentError('Invalid action');
    }
  }

  // Map action to its corresponding characteristic UUID
  String _mapActionToCharacteristicUuid(String action) {
    switch (action) {
      case 'powerOff':
        return BluetoothConstants.powerStateUUID;
      case 'startTherapy':
      case 'pauseTherapy':
      case 'therapyState':
        return BluetoothConstants.therapyStateUUID;
      case 'increaseHeat':
        return BluetoothConstants.increaseTemperatureUUID;
      case 'decreaseHeat':
        return BluetoothConstants.decreaseTemperatureUUID;
      case 'increaseTens':
        return BluetoothConstants.increaseTensIntensityUUID;
      case 'decreaseTens':
        return BluetoothConstants.decreaseTensIntensityUUID;
      case 'activeTens':
        return BluetoothConstants.activeTensUUID;
      case 'setTensMode':
        return BluetoothConstants.tensModeUUID;
      case 'targetTemp':
        return BluetoothConstants.targetTemperatureUUID;
      case 'currentTemp':
        return BluetoothConstants.currentTemperatureUUID;
      case 'targetTens':
        return BluetoothConstants.targetTensIntensityUUID;
      case 'currentTens':
        return BluetoothConstants.currentTensIntensityUUID;
      case 'measuredIntensity':
        return BluetoothConstants.measuredIntensityUUID;
      case 'tensStatus':
        return BluetoothConstants.tensStatus;
      case 'heatStatus':
        return BluetoothConstants.heatStatus;
      case 'tensControl':
        return BluetoothConstants.tensControlUUID;
      default:
        throw ArgumentError('Invalid action');
    }
  }

  // Retrieve the service by UUID
  Future<BluetoothService> _getService(String uuid) async {
    return bluetoothService
        .where((service) => service.uuid == Guid(uuid))
        .first;
  }

  // Retrieve the characteristic within the service by UUID
  Future<BluetoothCharacteristic> _getCharacteristic(
      BluetoothService service, String uuid) async {
    service.characteristics.forEach((element) {});

    return service.characteristics
        .where((characteristic) => characteristic.uuid == Guid(uuid))
        .first;
  }

// Method to run reconnect process
  @override
  Future<Either<BluetoothFailure, BluetoothDevice>> reconnectToDevice(
      BluetoothDevice device) async {
    try {
      await device.connect(
          timeout: const Duration(seconds: 10), autoConnect: false, mtu: null);
      bluetoothService = await device.discoverServices();
      await saveDevice(device); // Update saved device information
      return Right(device);
    } catch (e) {
      return const Left(BluetoothFailure.reconnectFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, DeviceModel>> getRecentDevice() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final recentDeviceJson = prefs.getString('recentDevice');
      if (recentDeviceJson != null) {
        final recentDevice = DeviceModel.fromJson(jsonDecode(recentDeviceJson));
        return Right(recentDevice);
      } else {
        return const Left(BluetoothFailure.getSavedDeviceFailed());
      }
    } catch (e) {
      return const Left(BluetoothFailure.getSavedDeviceFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, void>> stopScanning() async {
    try {
      _shouldStopScan = true;
      await FlutterBluePlus.stopScan();
      return const Right(unit);
    } catch (e) {
      return const Left(BluetoothFailure.stopScanningFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, BluetoothDevice>> getConnectedDevice() async {
    try {
      return Right(bluetoothDevice);
    } catch (e) {
      return Left(BluetoothFailure.getSavedDeviceFailed());
    }
  }
}
