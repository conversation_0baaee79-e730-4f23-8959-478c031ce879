import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:fpdart/fpdart.dart';
import '../application/bloc/sign_in/sign_in_bloc.dart'; // Importing SignInBloc from your application
import '../domain/failure/auth_failure.dart'; // Importing AuthFailure from your domain
import '../widgets/text_form_feild.dart'; // Importing custom widget MyTextFormField

class ResetPasswordWrapper extends StatelessWidget {
  final Widget child;
  final void Function(Unit)
      onRegisterSuccess; // Callback function for registration success
  final void Function(AuthFailure)
      onRegisterFailure; // Callback function for registration failure

  ResetPasswordWrapper({
    required this.child,
    required this.onRegisterSuccess,
    required this.onRegisterFailure,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignInBloc, SignInState>(
      listener: (context, state) {
        state.passwordResetOption.map(
          // () {},  // If passwordResetOption is None, do nothing
          (either) => either.mapBoth(
            onLeft: (failure) async {
              // Handle authentication failure
              Fluttertoast.showToast(
                msg: failure.map(
                  cancelledByUser: (_) => 'Cancelled',
                  serverError: (_) => 'Server error',
                  emailAlreadyInUse: (_) => 'Email already in use',
                  invalidEmailAndPasswordCombination: (_) =>
                      'Invalid email and password combination',
                  userNotFound: (_) => 'User not found',
                  emailVerificationFailed: (_) => 'Email verification failed',
                  emailVerificationSendFailure: (_) =>
                      'Email verification email was not sent',
                ),
                toastLength: Toast.LENGTH_LONG,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.red,
                textColor: Colors.white,
              );
              onRegisterFailure(failure); // Call callback function for failure
            },
            onRight: (success) {
              // Handle authentication success
              Fluttertoast.showToast(
                msg: 'Password reset email sent',
                toastLength: Toast.LENGTH_LONG,
                gravity: ToastGravity.BOTTOM,
               
              );
              onRegisterSuccess(unit); // Call callback function for success
            },
          ),
        );
      },
      builder: (context, state) {
        return Form(
          autovalidateMode: AutovalidateMode.disabled,
          child: ListView(
            physics: NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(8.0),
            children: <Widget>[
              MyTextFormField(
                heading: 'Email',
                keyboardtype: TextInputType.emailAddress,
                isEmail: true,
                onchanged: (value) => context.read<SignInBloc>().add(
                    SignInEvent.emailChanged(
                        value ?? '')), // Handle null with a default
                validator: (_) =>
                    context.read<SignInBloc>().state.emailAddress.isValid()
                        ? null
                        : 'Invalid Email',
              ),
              const SizedBox(height: 8),
              const SizedBox(height: 40),
              GestureDetector(
                onTap: () {
                  context.read<SignInBloc>().add(SignInEvent.forgotPassword(
                        context
                                .read<SignInBloc>()
                                .state
                                .emailAddress
                                .value
                                .getOrNull() ??
                            '', // Handle null with a default
                      ));
                },
                child: child, // Display the child widget inside GestureDetector
              ),
            ],
          ),
        );
      },
    );
  }
}
  /*
  In the above code snippet, we have created a  ResetPasswordWrapper  widget that wraps the child widget inside a  GestureDetector . The  ResetPasswordWrapper  widget listens to the  SignInBloc  state and triggers the  onRegisterSuccess  and  onRegisterFailure  callback functions based on the state.
  The  onRegisterSuccess  and  onRegisterFailure  callback functions are passed as parameters to the  ResetPasswordWrapper  widget. These callback functions are used to handle the success and failure scenarios of the password reset operation.
  The  GestureDetector  widget is used to handle the tap event on the child widget. When the user taps on the child widget, the  ResetPasswordWrapper  widget triggers the  SignInBloc  event to send a password reset email to the user.
  The  SignInBloc  state is used to handle the success and failure scenarios of the password reset operation. If the password reset operation is successful, the  onRegisterSuccess  callback function is called. If the password reset operation fails, the  onRegisterFailure  callback function is called.
  The  MyTextFormField  widget is a custom widget that displays a text form field with a heading and validation. It is used to capture the user's email address for the password reset operation.
  The  ResetPasswordWrapper  widget is a reusable widget that can be used to handle the password reset operation in different parts of the application. It encapsulates the logic for handling the success and failure scenarios of the password reset operation and provides a clean and consistent way to handle the password reset operation.
  Conclusion
  In this article, we have discussed how to handle the password reset operation in a Flutter application using the BLoC pattern. We have created a  ResetPasswordWrapper  widget that encapsulates the logic for handling the password reset operation and provides a clean and consistent way to handle the success and failure scenarios of the password reset operation.
  The  ResetPasswordWrapper  widget listens to the  SignInBloc  state and triggers the  onRegisterSuccess  and  onRegisterFailure  callback functions based on the state. The  onRegisterSuccess  and  onRegisterFailure  callback functions are used to handle the success and failure scenarios of the password reset operation.
  The  GestureDetector  widget is used to handle the tap event on the child widget. When the user taps on the child widget, the  ResetPasswordWrapper  widget triggers the  SignInBloc  event to send a password*/