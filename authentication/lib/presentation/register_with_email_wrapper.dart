import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../application/bloc/sign_in/sign_in_bloc.dart';
import '../domain/failure/auth_failure.dart';
import '../domain/model/user_model.dart';
import '../widgets/text_form_feild.dart';

class EmailRegisterWrapper extends StatelessWidget {
  final Widget child;
  final void Function(UserModel) onRegisterSuccess;
  final void Function(AuthFailure) onRegisterFailure;

  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();

  EmailRegisterWrapper({
    required this.child,
    required this.onRegisterSuccess,
    required this.onRegisterFailure,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignInBloc, SignInState>(
      listener: (context, state) {
        state.authFailureOrSuccessOption.map(
          (either) => either.mapBoth(
            onLeft: (failure) async {
              Fluttertoast.showToast(
                msg: failure.map(
                  cancelledByUser: (_) => 'Cancelled',
                  serverError: (_) => 'Server error',
                  emailAlreadyInUse: (_) => 'Email already in use',
                  invalidEmailAndPasswordCombination: (_) =>
                      'Invalid email and password combination',
                  userNotFound: (_) => 'User not found',
                  emailVerificationFailed: (_) => 'Email verification failed',
                  emailVerificationSendFailure: (_) =>
                      'Email verification email was not sent',
                ),
                toastLength: Toast.LENGTH_LONG,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.red,
                textColor: Colors.white,
              );
              onRegisterFailure(failure);
            },
            onRight: (success) => onRegisterSuccess(success),
          ),
        );
      },
      builder: (context, state) {
        return Form(
          autovalidateMode: AutovalidateMode.disabled,
          child: ListView(
            physics: NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(8.0),
            children: <Widget>[
              MyTextFormField(
                heading: 'Email',
                refkey: const Key('email'),
                keyboardtype: TextInputType.emailAddress,
                isEmail: true,
                focusNode: _emailFocusNode,
                textAction: TextInputAction.next,
                onFieldsubmitted: (_) {
                  FocusScope.of(context).requestFocus(_passwordFocusNode);
                },
                onchanged: (value) => context
                    .read<SignInBloc>()
                    .add(SignInEvent.emailChanged(value ?? '')),
                validator: (_) =>
                    context.read<SignInBloc>().state.emailAddress.isValid()
                        ? null
                        : 'Invalid Email',
              ),
              const SizedBox(height: 8),
              MyTextFormField(
                refkey: const Key('password'),
                heading: 'Password',
                isPassword: false,
                focusNode: _passwordFocusNode,
                textAction: TextInputAction.next,
                onFieldsubmitted: (_) {
                  FocusScope.of(context)
                      .requestFocus(_confirmPasswordFocusNode);
                },
                onchanged: (value) => context
                    .read<SignInBloc>()
                    .add(SignInEvent.passwordChanged(value ?? '')),
                validator: (_) =>
                    context.read<SignInBloc>().state.password.isValid()
                        ? null
                        : 'Password too short',
              ),
              const SizedBox(height: 8),
              MyTextFormField(
                refkey: const Key('confirmpassword'),
                heading: 'Confirm Password',
                isPassword: true,
                controller: _confirmPasswordController,
                focusNode: _confirmPasswordFocusNode,
                textAction: TextInputAction.done,
                onFieldsubmitted: (_) {
                  FocusScope.of(context).unfocus();
                },
                validator: (_) {
                  if (_confirmPasswordController.text !=
                      context.read<SignInBloc>().state.password.getOrElse('')) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 40),
              GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  context.read<SignInBloc>().add(
                        SignInEvent.registerWithEmail(
                          context
                                  .read<SignInBloc>()
                                  .state
                                  .emailAddress
                                  .value
                                  .getOrNull() ??
                              '',
                          context
                                  .read<SignInBloc>()
                                  .state
                                  .password
                                  .value
                                  .getOrNull() ??
                              '',
                        ),
                      );
                },
                child: child,
              ),
            ],
          ),
        );
      },
    );
  }
}
