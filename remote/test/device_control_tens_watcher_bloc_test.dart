import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:fpdart/fpdart.dart';
import 'package:remote/application/device_control_Tens_watcher_bloc/device_control_tens_watcher_bloc.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/model/tens_level_model.dart';

class MockRemoteControlFacade extends Mock implements RemoteControlFacade {}

void main() {
  late MockRemoteControlFacade mockRemoteRepository;
  late DeviceControlTensWatcherBloc bloc;

  setUp(() {
    mockRemoteRepository = MockRemoteControlFacade();
    bloc = DeviceControlTensWatcherBloc(mockRemoteRepository);
  });

  group('DeviceControlTensWatcherBloc', () {
    final TensLevel =
        TensLevelModel(selectedTensLevel: 5, actualTensLevel: 4, mode: 1);
    const remoteFailure = RemoteFailure.unexpectedFailure('Unexpected Failure');

    Stream<Either<RemoteFailure, TensLevelModel>> mockTensLevelStream(
        List<Either<RemoteFailure, TensLevelModel>> responses) {
      return Stream.fromIterable(responses);
    }

    blocTest<DeviceControlTensWatcherBloc, DeviceControlTensWatcherState>(
      'emits failure state when stream fails',
      build: () {
        when(() => mockRemoteRepository.watchTensLevel()).thenAnswer(
            (_) => mockTensLevelStream([const Left(remoteFailure)]));
        return bloc;
      },
      act: (bloc) =>
          bloc.add(const DeviceControlTensWatcherEvent.watchAllStarted()),
      expect: () => [
        DeviceControlTensWatcherState.initial(),
        DeviceControlTensWatcherState(
          selectedTensLevel: 0,
          actualTensLevel: 0,
          failureOrSuccessOption: Some(const Left(remoteFailure)),
          selectedMode: 1,
        ),
      ],
    );

    blocTest<DeviceControlTensWatcherBloc, DeviceControlTensWatcherState>(
      'emits updated state when Tens level changes successfully',
      build: () {
        when(() => mockRemoteRepository.watchTensLevel())
            .thenAnswer((_) => mockTensLevelStream([Right(TensLevel)]));
        return bloc;
      },
      act: (bloc) =>
          bloc.add(const DeviceControlTensWatcherEvent.watchAllStarted()),
      expect: () => [
        DeviceControlTensWatcherState.initial(),
        DeviceControlTensWatcherState(
          selectedTensLevel: TensLevel.selectedTensLevel!,
          actualTensLevel: TensLevel.actualTensLevel,
          selectedMode: TensLevel.mode,
          failureOrSuccessOption: Some(Right(unit)),
        ),
      ],
    );

    blocTest<DeviceControlTensWatcherBloc, DeviceControlTensWatcherState>(
      'updates state when Tens level changes multiple times',
      build: () {
        when(() => mockRemoteRepository.watchTensLevel()).thenAnswer(
          (_) => mockTensLevelStream([
            Right(TensLevelModel(
                selectedTensLevel: 3, actualTensLevel: 3, mode: 1)),
            Right(TensLevelModel(
                selectedTensLevel: 4, actualTensLevel: 4, mode: 1)),
          ]),
        );
        return bloc;
      },
      act: (bloc) =>
          bloc.add(const DeviceControlTensWatcherEvent.watchAllStarted()),
      expect: () => [
        DeviceControlTensWatcherState.initial(),
        const DeviceControlTensWatcherState(
          selectedTensLevel: 3,
          actualTensLevel: 3,
          selectedMode: 1,
          failureOrSuccessOption: Some(Right(unit)),
        ),
        DeviceControlTensWatcherState(
          selectedTensLevel: 4,
          actualTensLevel: 4,
          selectedMode: 1,
          failureOrSuccessOption: Some(Right(unit)),
        ),
      ],
    );

    blocTest<DeviceControlTensWatcherBloc, DeviceControlTensWatcherState>(
      'cancels subscription on close',
      build: () {
        when(() => mockRemoteRepository.watchTensLevel())
            .thenAnswer((_) => mockTensLevelStream([Right(TensLevel)]));
        return bloc;
      },
      act: (bloc) {
        bloc.add(const DeviceControlTensWatcherEvent.watchAllStarted());
      },
      wait: const Duration(
          milliseconds: 100), // Add a small delay to ensure event processing
      verify: (_) {
        verify(() => mockRemoteRepository.watchTensLevel()).called(1);
      },
      tearDown: () {
        bloc.close(); // Move bloc closing to tearDown
      },
    );

    blocTest<DeviceControlTensWatcherBloc, DeviceControlTensWatcherState>(
      'maintains selected level when state suddenly resets but actual level is received',
      build: () {
        when(() => mockRemoteRepository.watchTensLevel()).thenAnswer(
          (_) => mockTensLevelStream([
            // First emit a normal state update
            Right(TensLevelModel(
                selectedTensLevel: 6, actualTensLevel: 6, mode: 1)),
            // Then emit a "reset" state (simulating the bug we're seeing)
            Right(TensLevelModel(
                selectedTensLevel: 0, actualTensLevel: 6, mode: 1)),
          ]),
        );
        return bloc;
      },
      act: (bloc) =>
          bloc.add(const DeviceControlTensWatcherEvent.watchAllStarted()),
      expect: () => [
        DeviceControlTensWatcherState.initial(),
        // First update sets both selected and actual to 6
        DeviceControlTensWatcherState(
          selectedTensLevel: 6,
          actualTensLevel: 6,
          selectedMode: 1,
          failureOrSuccessOption: Some(Right(unit)),
        ),
        // Bug condition: When receiving a reset selected=0 with actual=6,
        // we should maintain the selected level from before
        DeviceControlTensWatcherState(
          selectedTensLevel:
              6, // We expect to maintain selected=6 even when model has 0
          actualTensLevel: 6,
          selectedMode: 1,
          failureOrSuccessOption: Some(Right(unit)),
        ),
      ],
    );
  });
}
