const functions = require("firebase-functions");
const admin = require("firebase-admin");
const sgMail = require("@sendgrid/mail");

admin.initializeApp();
const db = admin.firestore();
const SENDGRID_API_KEY = functions.config().sendgrid.api_key;

sgMail.setApiKey(SENDGRID_API_KEY);


/**
 * Helper function to create user document structure
 * @param {Object} user - Firebase user object
 * @return {Object} User document data
 */
function createUserDocumentData(user) {
  return {
    uid: user.uid,
    userEmail: user.email,
    userName: user.displayName || (user.email ? user.email.split("@")[0] : "User"),
    photoURL: user.photoURL,
    providerData: user.providerData,
    signUpTimeStamp: user.metadata.creationTime,
    loginTimeStamp: user.metadata.lastSignInTime,
    isOnboarded: false, // Initialize onboarding status as false
  };
}

exports.sendWelcomeEmailAndCreateUserDoc = functions.auth.user().onCreate(async (user) => {
  const userEmail = user.email;
  const userName = user.displayName || "User";
  const userDoc = createUserDocumentData(user);

  // Attempt to create the user document in Firestore
  try {
    const usersCollection = db.collection("users");
    await usersCollection.doc(userDoc.uid).set(userDoc, {merge: true});
    console.log("User document created successfully for:", user.uid);
  } catch (error) {
    console.error("Error during Firestore operation:", error);
    console.error("Failed Firestore path:", `users/${user.uid}`);
    console.error("Error details:", error);
  }

  // Configure the email message
  const msg = {
    to: userEmail,
    from: {"email": "<EMAIL>", "name": "Juno Technologies"},
    templateId: "d-4cc107f257aa4434aced7519b56eeef5",
    dynamicTemplateData: {
      username: userName,
    },
  };

  // Attempt to send the welcome email
  try {
    await sgMail.send(msg);
    console.log("Welcome email sent successfully to:", userEmail);
  } catch (error) {
    console.error("Failed to send welcome email:", error);
    console.error("Response body:", error.response.body);
  }
});

/**
 * Callable function to create user document for existing authenticated users
 * This function is called when a user is authenticated but their document doesn't exist
 * @param {Object} data - Function call data (unused)
 * @param {Object} context - Function call context
 * @return {Object} Result object
 */
exports.createMissingUserDocument = functions.https.onCall(async (_, context) => {
  // Check if user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError("unauthenticated", "User must be authenticated to call this function.");
  }

  const uid = context.auth.uid;

  try {
    // Check if user document already exists
    const userDocRef = db.collection("users").doc(uid);
    const userDocSnapshot = await userDocRef.get();

    if (userDocSnapshot.exists) {
      console.log("User document already exists for:", uid);
      return {success: true, message: "User document already exists", created: false};
    }

    // Get user record from Firebase Auth to get complete user data
    const userRecord = await admin.auth().getUser(uid);

    // Create user document data
    const userDoc = createUserDocumentData(userRecord);

    // Create the user document in Firestore
    await userDocRef.set(userDoc, {merge: true});

    console.log("Missing user document created successfully for:", uid);
    return {
      success: true,
      message: "User document created successfully",
      created: true,
      userData: userDoc,
    };
  } catch (error) {
    console.error("Error creating missing user document:", error);
    console.error("User ID:", uid);
    console.error("Error details:", error);

    throw new functions.https.HttpsError("internal", "Failed to create user document", {
      uid: uid,
      error: error.message,
    });
  }
});
